{% extends "base.html" %}

{% block title %}Performance Analytics - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">Performance Analytics</h2>
                        <p class="text-muted mb-0">Model performance metrics and prediction accuracy</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success">Models Active</span>
                        <span class="badge bg-info ms-2">Ensemble Mode</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Performance Overview -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Ensemble Accuracy</h6>
                    <i class="fas fa-bullseye text-success"></i>
                </div>
                <div class="metric-value text-success" id="ensemble-accuracy">87.3%</div>
                <div class="metric-change">
                    <small class="text-success">+2.1% this week</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Prediction Confidence</h6>
                    <i class="fas fa-brain text-info"></i>
                </div>
                <div class="metric-value text-info" id="prediction-confidence">92.1%</div>
                <div class="metric-change">
                    <small class="text-muted">Current signal</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Model Consensus</h6>
                    <i class="fas fa-handshake text-warning"></i>
                </div>
                <div class="metric-value text-warning" id="model-consensus">94.5%</div>
                <div class="metric-change">
                    <small class="text-success">Strong agreement</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Signal Strength</h6>
                    <i class="fas fa-signal text-primary"></i>
                </div>
                <div class="metric-value text-primary" id="signal-strength">8.7/10</div>
                <div class="metric-change">
                    <small class="text-success">Very Strong</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Model Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Individual Model Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- LightGBM -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="model-card">
                                <div class="model-header">
                                    <h6 class="mb-1">LightGBM</h6>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="model-metrics">
                                    <div class="metric-row">
                                        <span>Accuracy:</span>
                                        <span class="fw-bold text-success">89.2%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Confidence:</span>
                                        <span class="fw-bold">94.1%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Last Update:</span>
                                        <span class="text-muted">2 min ago</span>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: 89.2%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- CatBoost -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="model-card">
                                <div class="model-header">
                                    <h6 class="mb-1">CatBoost</h6>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="model-metrics">
                                    <div class="metric-row">
                                        <span>Accuracy:</span>
                                        <span class="fw-bold text-success">86.8%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Confidence:</span>
                                        <span class="fw-bold">91.3%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Last Update:</span>
                                        <span class="text-muted">2 min ago</span>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-info" style="width: 86.8%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- XGBoost -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="model-card">
                                <div class="model-header">
                                    <h6 class="mb-1">XGBoost</h6>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="model-metrics">
                                    <div class="metric-row">
                                        <span>Accuracy:</span>
                                        <span class="fw-bold text-warning">84.5%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Confidence:</span>
                                        <span class="fw-bold">88.7%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Last Update:</span>
                                        <span class="text-muted">2 min ago</span>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-warning" style="width: 84.5%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Linear Model -->
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="model-card">
                                <div class="model-header">
                                    <h6 class="mb-1">Linear</h6>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="model-metrics">
                                    <div class="metric-row">
                                        <span>Accuracy:</span>
                                        <span class="fw-bold text-info">82.1%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Confidence:</span>
                                        <span class="fw-bold">85.4%</span>
                                    </div>
                                    <div class="metric-row">
                                        <span>Last Update:</span>
                                        <span class="text-muted">2 min ago</span>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 6px;">
                                    <div class="progress-bar bg-secondary" style="width: 82.1%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Charts -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Model Accuracy Over Time</h5>
                </div>
                <div class="card-body">
                    <canvas id="accuracyChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Prediction Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="predictionChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature Importance and Model Insights -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Top Feature Importance</h5>
                </div>
                <div class="card-body">
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>RSI Divergence</span>
                                <span class="fw-bold">23.4%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 23.4%"></div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>DXY Correlation</span>
                                <span class="fw-bold">19.8%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: 19.8%"></div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Volume Profile</span>
                                <span class="fw-bold">16.2%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: 16.2%"></div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Session Momentum</span>
                                <span class="fw-bold">14.7%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-primary" style="width: 14.7%"></div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>VIX Sentiment</span>
                                <span class="fw-bold">12.1%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-secondary" style="width: 12.1%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Current Signal Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="signal-analysis">
                        <div class="signal-item">
                            <div class="signal-header">
                                <h6>Direction Prediction</h6>
                                <span class="badge bg-success">BULLISH</span>
                            </div>
                            <div class="signal-details">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Probability:</small><br>
                                        <span class="fw-bold text-success">87.3%</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Confidence:</small><br>
                                        <span class="fw-bold">High</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="signal-item">
                            <div class="signal-header">
                                <h6>Entry Recommendation</h6>
                                <span class="badge bg-info">BUY</span>
                            </div>
                            <div class="signal-details">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Target Price:</small><br>
                                        <span class="fw-bold">$2,655.20</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Stop Loss:</small><br>
                                        <span class="fw-bold">$2,645.80</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="signal-item">
                            <div class="signal-header">
                                <h6>Risk Assessment</h6>
                                <span class="badge bg-warning">MODERATE</span>
                            </div>
                            <div class="signal-details">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Risk Score:</small><br>
                                        <span class="fw-bold">6.2/10</span>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Position Size:</small><br>
                                        <span class="fw-bold">0.12 lots</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Detailed Model Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Model</th>
                                    <th>Accuracy</th>
                                    <th>Precision</th>
                                    <th>Recall</th>
                                    <th>F1-Score</th>
                                    <th>AUC</th>
                                    <th>Predictions Today</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>LightGBM</strong></td>
                                    <td><span class="text-success">89.2%</span></td>
                                    <td>87.5%</td>
                                    <td>91.1%</td>
                                    <td>89.3%</td>
                                    <td>0.924</td>
                                    <td>247</td>
                                    <td><span class="badge bg-success">Excellent</span></td>
                                </tr>
                                <tr>
                                    <td><strong>CatBoost</strong></td>
                                    <td><span class="text-info">86.8%</span></td>
                                    <td>85.2%</td>
                                    <td>88.7%</td>
                                    <td>86.9%</td>
                                    <td>0.901</td>
                                    <td>247</td>
                                    <td><span class="badge bg-success">Good</span></td>
                                </tr>
                                <tr>
                                    <td><strong>XGBoost</strong></td>
                                    <td><span class="text-warning">84.5%</span></td>
                                    <td>82.8%</td>
                                    <td>86.4%</td>
                                    <td>84.6%</td>
                                    <td>0.887</td>
                                    <td>247</td>
                                    <td><span class="badge bg-warning">Fair</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Linear</strong></td>
                                    <td><span class="text-secondary">82.1%</span></td>
                                    <td>80.3%</td>
                                    <td>84.2%</td>
                                    <td>82.2%</td>
                                    <td>0.865</td>
                                    <td>247</td>
                                    <td><span class="badge bg-info">Baseline</span></td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>Ensemble</strong></td>
                                    <td><span class="text-success">87.3%</span></td>
                                    <td>86.1%</td>
                                    <td>88.9%</td>
                                    <td>87.5%</td>
                                    <td>0.912</td>
                                    <td>247</td>
                                    <td><span class="badge bg-primary">Active</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize performance page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializePerformanceCharts();
    
    // Set up real-time updates
    startPerformanceUpdates();
    
    // Set up WebSocket updates if available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        console.log('Performance page initialized with real-time updates');
    }
});

function initializePerformanceCharts() {
    // Initialize accuracy chart
    const accuracyCtx = document.getElementById('accuracyChart');
    if (accuracyCtx) {
        new Chart(accuracyCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'LightGBM',
                    data: [],
                    borderColor: '#00ff88',
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    borderWidth: 2
                }, {
                    label: 'CatBoost',
                    data: [],
                    borderColor: '#00aaff',
                    backgroundColor: 'rgba(0, 170, 255, 0.1)',
                    borderWidth: 2
                }, {
                    label: 'XGBoost',
                    data: [],
                    borderColor: '#ffaa00',
                    backgroundColor: 'rgba(255, 170, 0, 0.1)',
                    borderWidth: 2
                }, {
                    label: 'Ensemble',
                    data: [],
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#ffffff' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    }

    // Initialize prediction distribution chart
    const predictionCtx = document.getElementById('predictionChart');
    if (predictionCtx) {
        new Chart(predictionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Bullish', 'Bearish', 'Neutral'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: ['#00ff88', '#ff4444', '#ffaa00'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#ffffff' }
                    }
                }
            }
        });
    }
}

function startPerformanceUpdates() {
    // Update performance metrics every 30 seconds
    setInterval(updatePerformanceMetrics, 30000);
}

function updatePerformanceMetrics() {
    // This will be connected to real WebSocket data
    console.log('Updating performance metrics...');
}
</script>

<style>
.model-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.model-header {
    display: flex;
    justify-content-between;
    align-items-center;
    margin-bottom: 10px;
}

.model-metrics .metric-row {
    display: flex;
    justify-content-between;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.feature-item {
    margin-bottom: 15px;
}

.signal-analysis .signal-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.signal-header {
    display: flex;
    justify-content-between;
    align-items-center;
    margin-bottom: 10px;
}

.signal-details {
    font-size: 0.9rem;
}
</style>
{% endblock %}
