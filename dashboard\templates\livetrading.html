{% extends "base.html" %}

{% block title %}Live Trading - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">Live Trading</h2>
                        <p class="text-muted mb-0">Real-time trading operations and positions</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success" id="trading-status">Trading Active</span>
                        <span class="badge bg-info ms-2">XAUUSD!</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Account Balance</h6>
                    <i class="fas fa-wallet text-success"></i>
                </div>
                <div class="metric-value text-success" id="account-balance">$10,245.67</div>
                <div class="metric-change">
                    <small class="text-muted">Available: <span id="available-margin">$8,156.23</span></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Daily P&L</h6>
                    <i class="fas fa-chart-line text-profit"></i>
                </div>
                <div class="metric-value text-profit" id="daily-pnl">+$125.45</div>
                <div class="metric-change">
                    <small class="text-success">+1.23% today</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Open Positions</h6>
                    <i class="fas fa-chart-area text-info"></i>
                </div>
                <div class="metric-value text-info" id="open-positions">3</div>
                <div class="metric-change">
                    <small class="text-muted">Total exposure: <span id="total-exposure">$15,420</span></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Win Rate Today</h6>
                    <i class="fas fa-percentage text-warning"></i>
                </div>
                <div class="metric-value text-warning" id="daily-winrate">75.0%</div>
                <div class="metric-change">
                    <small class="text-muted">12 trades completed</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Price and Trading Controls -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">XAUUSD! Live Price</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="price-display-large text-center">
                                <div class="current-price" id="live-price">$2,650.45</div>
                                <div class="price-change" id="live-change">
                                    <span class="text-success">+$2.15 (+0.08%)</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="price-info">
                                        <div class="price-label">Bid</div>
                                        <div class="price-value text-danger" id="bid-price">2650.23</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="price-info">
                                        <div class="price-label">Ask</div>
                                        <div class="price-value text-success" id="ask-price">2650.67</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <div class="price-info">
                                        <div class="price-label">Spread</div>
                                        <div class="price-value" id="spread">0.44</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="price-info">
                                        <div class="price-label">Volume</div>
                                        <div class="price-value" id="volume">1.2M</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="showOrderDialog('BUY')">
                            <i class="fas fa-arrow-up me-2"></i>Buy XAUUSD!
                        </button>
                        <button class="btn btn-danger" onclick="showOrderDialog('SELL')">
                            <i class="fas fa-arrow-down me-2"></i>Sell XAUUSD!
                        </button>
                        <button class="btn btn-warning" onclick="closeAllPositions()">
                            <i class="fas fa-times-circle me-2"></i>Close All
                        </button>
                        <button class="btn btn-info" onclick="refreshPositions()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Open Positions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Open Positions</h5>
                    <span class="badge bg-info" id="positions-count">3 positions</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>Type</th>
                                    <th>Volume</th>
                                    <th>Open Price</th>
                                    <th>Current Price</th>
                                    <th>P&L</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="positions-table">
                                <tr>
                                    <td><strong>XAUUSD!</strong></td>
                                    <td><span class="badge bg-success">BUY</span></td>
                                    <td>0.10</td>
                                    <td>2648.25</td>
                                    <td>2650.45</td>
                                    <td class="text-success">+$22.00</td>
                                    <td>14:32:15</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="closePosition(1)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>XAUUSD!</strong></td>
                                    <td><span class="badge bg-danger">SELL</span></td>
                                    <td>0.05</td>
                                    <td>2651.80</td>
                                    <td>2650.45</td>
                                    <td class="text-success">+$6.75</td>
                                    <td>14:28:42</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="closePosition(2)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>XAUUSD!</strong></td>
                                    <td><span class="badge bg-success">BUY</span></td>
                                    <td>0.15</td>
                                    <td>2649.10</td>
                                    <td>2650.45</td>
                                    <td class="text-success">+$20.25</td>
                                    <td>14:15:08</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="closePosition(3)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Trades -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Trades</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Symbol</th>
                                    <th>Type</th>
                                    <th>Volume</th>
                                    <th>Price</th>
                                    <th>P&L</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="recent-trades">
                                <tr>
                                    <td>14:45:23</td>
                                    <td>XAUUSD!</td>
                                    <td><span class="badge bg-success">BUY</span></td>
                                    <td>0.08</td>
                                    <td>2649.85</td>
                                    <td class="text-success">+$18.40</td>
                                    <td><span class="badge bg-success">Closed</span></td>
                                </tr>
                                <tr>
                                    <td>14:38:17</td>
                                    <td>XAUUSD!</td>
                                    <td><span class="badge bg-danger">SELL</span></td>
                                    <td>0.12</td>
                                    <td>2652.30</td>
                                    <td class="text-success">+$28.80</td>
                                    <td><span class="badge bg-success">Closed</span></td>
                                </tr>
                                <tr>
                                    <td>14:22:45</td>
                                    <td>XAUUSD!</td>
                                    <td><span class="badge bg-success">BUY</span></td>
                                    <td>0.10</td>
                                    <td>2647.90</td>
                                    <td class="text-danger">-$15.20</td>
                                    <td><span class="badge bg-danger">Closed</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Trading Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-success">8</div>
                                <div class="stat-label">Winning Trades</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-danger">4</div>
                                <div class="stat-label">Losing Trades</div>
                            </div>
                        </div>
                    </div>
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-info">$28.50</div>
                                <div class="stat-label">Avg Win</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-warning">$12.30</div>
                                <div class="stat-label">Avg Loss</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="stat-item">
                            <div class="stat-value text-primary">2.32</div>
                            <div class="stat-label">Profit Factor</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Order Dialog Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title" id="orderModalTitle">Place Order</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="orderForm">
                    <div class="mb-3">
                        <label class="form-label">Symbol</label>
                        <input type="text" class="form-control" value="XAUUSD!" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Volume</label>
                        <input type="number" class="form-control" id="orderVolume" value="0.10" step="0.01" min="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stop Loss</label>
                        <input type="number" class="form-control" id="orderSL" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Take Profit</label>
                        <input type="number" class="form-control" id="orderTP" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitOrder()">Place Order</button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize live trading page
document.addEventListener('DOMContentLoaded', function() {
    // Start real-time price updates
    startPriceUpdates();
    
    // Set up WebSocket updates if available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        console.log('Live trading page initialized with real-time updates');
    }
});

function startPriceUpdates() {
    // Update prices every second
    setInterval(updateLivePrices, 1000);
}

function updateLivePrices() {
    // This will be connected to real WebSocket data
    console.log('Updating live prices...');
}

function showOrderDialog(type) {
    const modal = new bootstrap.Modal(document.getElementById('orderModal'));
    document.getElementById('orderModalTitle').textContent = `Place ${type} Order`;
    modal.show();
}

function submitOrder() {
    // Submit order logic
    console.log('Submitting order...');
    const modal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
    modal.hide();
}

function closePosition(positionId) {
    if (confirm('Are you sure you want to close this position?')) {
        console.log('Closing position:', positionId);
    }
}

function closeAllPositions() {
    if (confirm('Are you sure you want to close ALL positions?')) {
        console.log('Closing all positions...');
    }
}

function refreshPositions() {
    console.log('Refreshing positions...');
}
</script>

<style>
.price-display-large .current-price {
    font-size: 3rem;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.price-info {
    padding: 10px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.price-label {
    font-size: 0.8rem;
    color: #888;
    text-transform: uppercase;
}

.price-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.stat-item {
    padding: 10px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.8rem;
    color: #888;
    text-transform: uppercase;
}
</style>
{% endblock %}
