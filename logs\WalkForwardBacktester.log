2025-09-20 17:28:05,665 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:28:05,667 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:28:05,669 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:28:05,670 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 17:28:05,672 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-20 17:28:29,217 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:28:29,218 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:28:29,220 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:28:29,220 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 17:28:29,221 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-20 17:29:14,627 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:29:14,628 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:29:14,629 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:29:14,630 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 17:29:14,631 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-20 17:30:32,138 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:30:32,140 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:30:32,141 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:30:32,141 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-07-03 21:45:00 to 2025-07-07 21:35:00
2025-09-20 17:30:32,142 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 15.0, Testing days: 7.5, Step days: 7.5
2025-09-20 17:30:32,142 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 17:30:32,143 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-20 17:30:51,870 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:30:51,872 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:30:51,873 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:30:51,873 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 17:30:51,873 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 15.0, Testing days: 7.5, Step days: 7.5
2025-09-20 17:30:51,874 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 17:30:51,875 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-20 17:31:24,252 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 17:31:24,255 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 17:31:24,256 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 17:31:24,256 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 17:31:24,256 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 17:31:24,257 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 17:31:24,258 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 17:31:24,258 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 17:31:24,258 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 17:31:51,795 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-60848.02
2025-09-20 17:31:51,795 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 17:32:28,766 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $1871.98
2025-09-20 17:32:28,774 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 64.52s
2025-09-20 21:24:05,182 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 21:24:05,184 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 21:24:05,185 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 21:24:05,185 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 21:24:05,185 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 21:24:05,186 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 21:24:05,187 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 21:24:05,187 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 21:24:05,188 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 21:24:32,235 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-60848.02
2025-09-20 21:24:32,236 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 21:25:09,469 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $1871.98
2025-09-20 21:25:09,476 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 64.29s
2025-09-20 22:29:51,546 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:29:51,547 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:29:51,548 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:29:51,549 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:29:51,549 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:29:51,550 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:29:51,550 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:29:51,551 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:29:51,551 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:30:18,011 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:30:18,011 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:30:54,433 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:30:54,441 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 62.89s
2025-09-20 22:31:35,614 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:31:35,615 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:31:35,616 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:31:35,616 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:31:35,617 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:31:35,617 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:31:35,618 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:31:35,618 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:31:35,618 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:32:02,750 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:32:02,751 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:32:39,614 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:32:39,623 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 64.01s
2025-09-20 22:35:18,339 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:35:18,341 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:35:18,341 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:35:18,342 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:35:18,342 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:35:18,343 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:35:18,344 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:35:18,344 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:35:18,345 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:35:46,678 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:35:46,679 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:36:26,677 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:36:26,686 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 68.34s
2025-09-20 22:37:39,410 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:37:39,411 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:37:39,412 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:37:39,412 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:37:39,412 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:37:39,413 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:37:39,413 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:37:39,414 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:37:39,414 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:38:06,793 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:38:06,793 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:38:43,856 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:38:43,864 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 64.45s
2025-09-20 22:40:47,348 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:40:47,350 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:40:47,350 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:40:47,351 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:40:47,351 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:40:47,351 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:40:47,352 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:40:47,352 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:40:47,352 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:41:15,677 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:41:15,677 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:41:54,285 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:41:54,293 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 66.94s
2025-09-20 22:43:10,959 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:43:10,961 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:43:10,962 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:43:10,962 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:43:10,963 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:43:10,965 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:43:10,965 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:43:10,966 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:43:10,966 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:43:38,170 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:43:38,171 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:44:15,369 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:44:15,378 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 64.42s
2025-09-20 22:45:21,250 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:45:21,251 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:45:21,252 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:45:21,253 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:45:21,253 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:45:21,253 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:45:21,254 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:45:21,255 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:45:21,255 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:45:48,882 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:45:48,882 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:46:27,740 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:46:27,748 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 66.50s
2025-09-20 22:47:30,035 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:47:30,036 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:47:30,037 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:47:30,037 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:47:30,037 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:47:30,038 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:47:30,039 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:47:30,039 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:47:30,039 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:47:58,464 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:47:58,464 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:48:36,824 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:48:36,833 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 66.80s
2025-09-20 22:49:10,012 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 22:49:10,013 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 22:49:10,014 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 22:49:10,014 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:49:10,015 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 6.0, Testing days: 3.0, Step days: 3.0
2025-09-20 22:49:10,015 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-23 01:00:00 to 2025-06-29 01:00:00 (1380 samples), Test 2025-06-29 02:00:00 to 2025-07-02 02:00:00 (565 samples)
2025-09-20 22:49:10,016 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:227 - Created window: Train 2025-06-26 01:00:00 to 2025-07-02 01:00:00 (1105 samples), Test 2025-07-02 02:00:00 to 2025-07-05 02:00:00 (765 samples)
2025-09-20 22:49:10,016 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-20 22:49:10,016 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-06-29 01:00:00, Test: 2025-06-29 02:00:00 to 2025-07-02 02:00:00
2025-09-20 22:49:36,713 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-57312.03
2025-09-20 22:49:36,713 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-02 01:00:00, Test: 2025-07-02 02:00:00 to 2025-07-05 02:00:00
2025-09-20 22:50:13,034 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $4119.97
2025-09-20 22:50:13,041 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 63.03s
2025-09-20 23:00:10,540 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-20 23:00:10,542 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-20 23:00:10,543 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-20 23:00:10,544 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 23:00:10,545 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 180, Testing days: 30, Step days: 30
2025-09-20 23:00:10,545 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-20 23:00:10,547 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-21 00:53:56,151 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 00:53:56,152 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 00:53:56,153 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 00:53:56,153 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:191 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:53:56,153 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:192 - Training days: 180, Testing days: 30, Step days: 30
2025-09-21 00:53:56,153 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 0 walk-forward windows
2025-09-21 00:53:56,154 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.00s
2025-09-21 00:54:52,329 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 00:54:52,330 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 00:54:52,331 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 00:54:52,331 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:188 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:54:52,332 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:189 - Total data span: 14 days
2025-09-21 00:54:52,332 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:208 - Training days: 8, Testing days: 6, Step days: 3
2025-09-21 00:54:52,333 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-23 01:00:00 to 2025-07-01 01:00:00 (1657 samples), Test 2025-07-01 02:00:00 to 2025-07-07 02:00:00 (1054 samples)
2025-09-21 00:54:52,333 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-26 01:00:00 to 2025-07-04 01:00:00 (1657 samples), Test 2025-07-04 02:00:00 to 2025-07-07 21:35:00 (461 samples)
2025-09-21 00:54:52,333 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-21 00:54:52,334 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-07-01 01:00:00, Test: 2025-07-01 02:00:00 to 2025-07-07 02:00:00
2025-09-21 00:54:52,533 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 0 trades, PnL: $0.00
2025-09-21 00:54:52,533 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-04 01:00:00, Test: 2025-07-04 02:00:00 to 2025-07-07 21:35:00
2025-09-21 00:54:52,648 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 0 trades, PnL: $0.00
2025-09-21 00:54:52,650 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.32s
2025-09-21 00:57:42,185 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 00:57:42,186 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 00:57:42,187 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 00:57:42,187 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:188 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:57:42,188 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:189 - Total data span: 14 days
2025-09-21 00:57:42,188 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:208 - Training days: 8, Testing days: 6, Step days: 3
2025-09-21 00:57:42,188 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-23 01:00:00 to 2025-07-01 01:00:00 (1657 samples), Test 2025-07-01 02:00:00 to 2025-07-07 02:00:00 (1054 samples)
2025-09-21 00:57:42,189 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-26 01:00:00 to 2025-07-04 01:00:00 (1657 samples), Test 2025-07-04 02:00:00 to 2025-07-07 21:35:00 (461 samples)
2025-09-21 00:57:42,189 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-21 00:57:42,190 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-07-01 01:00:00, Test: 2025-07-01 02:00:00 to 2025-07-07 02:00:00
2025-09-21 00:57:42,400 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 0 trades, PnL: $0.00
2025-09-21 00:57:42,401 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-04 01:00:00, Test: 2025-07-04 02:00:00 to 2025-07-07 21:35:00
2025-09-21 00:57:42,511 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 0 trades, PnL: $0.00
2025-09-21 00:57:42,512 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.33s
2025-09-21 00:59:35,995 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 00:59:35,998 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 00:59:36,002 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 00:59:36,003 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:188 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:59:36,003 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:189 - Total data span: 14 days
2025-09-21 00:59:36,003 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:208 - Training days: 8, Testing days: 6, Step days: 3
2025-09-21 00:59:36,004 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-23 01:00:00 to 2025-07-01 01:00:00 (1657 samples), Test 2025-07-01 02:00:00 to 2025-07-07 02:00:00 (1054 samples)
2025-09-21 00:59:36,005 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-26 01:00:00 to 2025-07-04 01:00:00 (1657 samples), Test 2025-07-04 02:00:00 to 2025-07-07 21:35:00 (461 samples)
2025-09-21 00:59:36,005 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-21 00:59:36,005 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-07-01 01:00:00, Test: 2025-07-01 02:00:00 to 2025-07-07 02:00:00
2025-09-21 00:59:36,134 - WalkForwardBacktester - INFO - _run_window_backtest:291 - Trade executed at 2025-07-01 02:00:00: short 8.0 lots
2025-09-21 01:00:31,902 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 1 trades, PnL: $-18232.02
2025-09-21 01:00:31,902 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-04 01:00:00, Test: 2025-07-04 02:00:00 to 2025-07-07 21:35:00
2025-09-21 01:00:32,015 - WalkForwardBacktester - INFO - _run_window_backtest:291 - Trade executed at 2025-07-04 02:05:00: short 8.0 lots
2025-09-21 01:00:55,208 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 1 trades, PnL: $-2296.02
2025-09-21 01:00:55,223 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 79.22s
2025-09-21 01:01:11,799 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 01:01:11,801 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 01:01:11,801 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 01:01:11,802 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:188 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 01:01:11,802 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:189 - Total data span: 14 days
2025-09-21 01:01:11,802 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:208 - Training days: 8, Testing days: 6, Step days: 3
2025-09-21 01:01:11,803 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-23 01:00:00 to 2025-07-01 01:00:00 (1657 samples), Test 2025-07-01 02:00:00 to 2025-07-07 02:00:00 (1054 samples)
2025-09-21 01:01:11,803 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-26 01:00:00 to 2025-07-04 01:00:00 (1657 samples), Test 2025-07-04 02:00:00 to 2025-07-07 21:35:00 (461 samples)
2025-09-21 01:01:11,804 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-21 01:01:11,804 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-07-01 01:00:00, Test: 2025-07-01 02:00:00 to 2025-07-07 02:00:00
2025-09-21 01:01:12,063 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 0 trades, PnL: $0.00
2025-09-21 01:01:12,063 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-04 01:00:00, Test: 2025-07-04 02:00:00 to 2025-07-07 21:35:00
2025-09-21 01:01:12,209 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 0 trades, PnL: $0.00
2025-09-21 01:01:12,211 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.41s
2025-09-21 01:02:07,237 - WalkForwardBacktester - INFO - __init__:146 - Initialized WalkForwardBacktester
2025-09-21 01:02:07,238 - WalkForwardBacktester - INFO - __init__:53 - Walk-forward backtester initialized
2025-09-21 01:02:07,239 - WalkForwardBacktester - INFO - run_backtest:80 - Starting walk-forward backtesting...
2025-09-21 01:02:07,239 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:188 - Data range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 01:02:07,239 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:189 - Total data span: 14 days
2025-09-21 01:02:07,239 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:208 - Training days: 8, Testing days: 6, Step days: 3
2025-09-21 01:02:07,240 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-23 01:00:00 to 2025-07-01 01:00:00 (1657 samples), Test 2025-07-01 02:00:00 to 2025-07-07 02:00:00 (1054 samples)
2025-09-21 01:02:07,240 - WalkForwardBacktester - INFO - _generate_walk_forward_windows:246 - Created window: Train 2025-06-26 01:00:00 to 2025-07-04 01:00:00 (1657 samples), Test 2025-07-04 02:00:00 to 2025-07-07 21:35:00 (461 samples)
2025-09-21 01:02:07,241 - WalkForwardBacktester - INFO - run_backtest:84 - Generated 2 walk-forward windows
2025-09-21 01:02:07,241 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 1/2: Train: 2025-06-23 01:00:00 to 2025-07-01 01:00:00, Test: 2025-07-01 02:00:00 to 2025-07-07 02:00:00
2025-09-21 01:02:07,448 - WalkForwardBacktester - INFO - run_backtest:119 - Window 1 completed: 0 trades, PnL: $0.00
2025-09-21 01:02:07,449 - WalkForwardBacktester - INFO - run_backtest:91 - Processing window 2/2: Train: 2025-06-26 01:00:00 to 2025-07-04 01:00:00, Test: 2025-07-04 02:00:00 to 2025-07-07 21:35:00
2025-09-21 01:02:07,583 - WalkForwardBacktester - INFO - run_backtest:119 - Window 2 completed: 0 trades, PnL: $0.00
2025-09-21 01:02:07,584 - WalkForwardBacktester - INFO - run_backtest:145 - Walk-forward backtesting completed in 0.35s
