"""
Configuration for Backtesting System

Provides all parameters needed for comprehensive backtesting including
transaction costs, risk parameters, and testing scenarios.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.config import Config


@dataclass
class BacktestConfig:
    """
    Configuration for the backtesting system.
    
    Provides all parameters needed for realistic backtesting including
    transaction costs, risk management, and testing scenarios.
    """
    
    # Base configuration
    base_config: Config
    
    # General backtesting settings
    initial_capital: float = 100000.0  # Starting capital in USD
    enable_caching: bool = True
    max_missing_data_percent: float = 5.0
    
    # Trading instrument settings (XAUUSD!)
    symbol: str = "XAUUSD!"
    pip_value: float = 1.0   # 1 pip = $1.00 per standard lot for XAUUSD
    min_trade_size: float = 0.01  # Minimum lot size
    max_trade_size: float = 10.0  # Maximum lot size
    
    # Transaction cost settings (realistic MT5 conditions)
    spread_pips: float = 0.18  # Average spread from data analysis
    commission_per_lot: float = 0.0  # Commission per standard lot
    slippage_pips: float = 0.05  # Expected slippage in pips
    
    # Risk management settings
    max_risk_per_trade: float = 0.02  # 2% risk per trade
    max_portfolio_risk: float = 0.06  # 6% maximum portfolio risk
    max_concurrent_trades: int = 3  # Maximum concurrent positions
    max_daily_trades: int = 10  # Maximum trades per day
    max_consecutive_losses: int = 5  # Stop trading after consecutive losses
    
    # Position sizing settings
    position_sizing_method: str = "fixed_risk"  # 'fixed_risk', 'fixed_size', 'kelly'
    base_position_size: float = 0.1  # Base position size in lots
    kelly_fraction: float = 0.25  # Kelly criterion fraction
    
    # Model-driven settings
    min_model_confidence: float = 0.55  # Minimum confidence to trade (lowered for realistic trading)
    consensus_threshold: float = 0.55  # Minimum consensus for trade (lowered for realistic trading)
    model_weights: Dict[str, float] = field(default_factory=lambda: {
        'lightgbm': 0.35,
        'catboost': 0.25,
        'xgboost': 0.25,
        'linear': 0.15
    })
    
    # Entry/Exit logic settings
    entry_config: Dict[str, Any] = field(default_factory=lambda: {
        'max_entry_delay_bars': 3,  # Maximum bars to wait for entry
        'entry_price_improvement_pips': 2.0,  # Try to improve entry by X pips
        'market_order_threshold': 0.8,  # Use market order if confidence > threshold
        'volume_confirmation_required': False,
        'cross_asset_confirmation_required': False  # Disabled due to missing features
    })
    
    # Multi-tier TP/SL system
    tp_sl_config: Dict[str, Any] = field(default_factory=lambda: {
        'tier1_percentage': 0.4,  # 40% of position for quick profits
        'tier2_percentage': 0.35,  # 35% for swing profits
        'tier3_percentage': 0.25,  # 25% for trend following
        'min_tp1_pips': 5.0,
        'max_tp1_pips': 50.0,
        'min_tp2_pips': 20.0,
        'max_tp2_pips': 150.0,
        'min_sl_pips': 8.0,
        'max_sl_pips': 80.0,
        'trailing_stop_enabled': True,
        'trailing_stop_distance_pips': 15.0,
        'breakeven_trigger_pips': 20.0
    })
    
    # Walk-forward validation settings
    walkforward_config: Dict[str, Any] = field(default_factory=lambda: {
        'training_window_months': 6,  # Training window size
        'testing_window_months': 1,   # Testing window size
        'step_size_months': 1,        # Step size for walk-forward
        'min_training_samples': 1000, # Minimum samples for training
        'rebalance_frequency': 'monthly',  # 'daily', 'weekly', 'monthly'
        'enable_model_retraining': False  # Whether to retrain models
    })
    
    # Time-based settings
    time_config: Dict[str, Any] = field(default_factory=lambda: {
        'trading_sessions': {
            'asian': {'start': '00:00', 'end': '08:00'},
            'european': {'start': '08:00', 'end': '16:00'},
            'us': {'start': '16:00', 'end': '24:00'}
        },
        'exclude_weekends': True,
        'exclude_holidays': True,
        'max_trade_duration_hours': 48,  # Maximum trade duration
        'session_based_sizing': True,    # Adjust sizing by session
        'avoid_news_events': True        # Avoid trading during major news
    })
    
    # Performance calculation settings
    performance_config: Dict[str, Any] = field(default_factory=lambda: {
        'benchmark_return': 0.02,  # 2% annual risk-free rate
        'confidence_level': 0.95,  # For VaR calculations
        'rolling_window_days': 30, # Rolling metrics window
        'min_trades_for_metrics': 10,  # Minimum trades for valid metrics
        'annualization_factor': 252    # Trading days per year
    })
    
    # Output configuration
    output_config: Dict[str, Any] = field(default_factory=lambda: {
        'save_trades': True,
        'save_equity_curve': True,
        'save_performance_metrics': True,
        'save_model_analysis': True,
        'generate_charts': True,
        'chart_format': 'png',
        'chart_dpi': 300,
        'results_format': 'csv',  # 'csv', 'json', 'both'
        'compression': False,
        'backup_enabled': True
    })
    
    # Logging configuration
    logging_config: Dict[str, Any] = field(default_factory=lambda: {
        'log_level': 'INFO',
        'log_trades': True,
        'log_signals': True,
        'log_performance': True,
        'log_errors': True,
        'detailed_logging': False
    })
    
    def validate(self) -> List[str]:
        """
        Validate configuration parameters.
        
        Returns:
            List of validation errors
        """
        errors = []
        
        if self.initial_capital <= 0:
            errors.append("Initial capital must be positive")
        
        if self.max_risk_per_trade <= 0 or self.max_risk_per_trade > 0.1:
            errors.append("Max risk per trade must be between 0 and 0.1 (10%)")
        
        if self.max_portfolio_risk <= self.max_risk_per_trade:
            errors.append("Max portfolio risk must be greater than max risk per trade")
        
        if self.min_model_confidence < 0.5 or self.min_model_confidence > 1.0:
            errors.append("Min model confidence must be between 0.5 and 1.0")
        
        if not (0.5 <= self.consensus_threshold <= 1.0):
            errors.append("Consensus threshold must be between 0.5 and 1.0")
        
        # Validate model weights sum to 1.0
        total_weight = sum(self.model_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            errors.append(f"Model weights must sum to 1.0, got {total_weight}")
        
        # Validate TP/SL tiers sum to 1.0
        tier_sum = (self.tp_sl_config['tier1_percentage'] + 
                   self.tp_sl_config['tier2_percentage'] + 
                   self.tp_sl_config['tier3_percentage'])
        if abs(tier_sum - 1.0) > 0.01:
            errors.append(f"TP/SL tier percentages must sum to 1.0, got {tier_sum}")
        
        return errors
    
    def get_risk_adjusted_position_size(self, account_balance: float, 
                                      stop_loss_pips: float) -> float:
        """
        Calculate risk-adjusted position size.
        
        Args:
            account_balance: Current account balance
            stop_loss_pips: Stop loss distance in pips
            
        Returns:
            Position size in lots
        """
        if stop_loss_pips <= 0:
            return self.min_trade_size
        
        risk_amount = account_balance * self.max_risk_per_trade
        pip_risk = stop_loss_pips * self.pip_value
        
        if pip_risk <= 0:
            return self.min_trade_size
        
        position_size = risk_amount / pip_risk
        
        # Apply limits
        position_size = max(self.min_trade_size, position_size)
        position_size = min(self.max_trade_size, position_size)
        
        return round(position_size, 2)
    
    def get_session_multiplier(self, timestamp: datetime) -> float:
        """
        Get position size multiplier based on trading session.
        
        Args:
            timestamp: Current timestamp
            
        Returns:
            Multiplier for position sizing
        """
        if not self.time_config['session_based_sizing']:
            return 1.0
        
        hour = timestamp.hour
        
        # Asian session (lower volatility, smaller positions)
        if 0 <= hour < 8:
            return 0.8
        # European session (normal volatility)
        elif 8 <= hour < 16:
            return 1.0
        # US session (higher volatility, larger positions)
        elif 16 <= hour < 24:
            return 1.2
        else:
            return 1.0
