2025-09-20 22:55:27,227 - BacktestRunner - INFO - __init__:44 - Initializing XAUUSD Backtesting System
2025-09-20 22:55:27,228 - BacktestRunner - INFO - load_data:49 - Loading data from data/cleaned
2025-09-20 22:55:27,229 - BacktestRunner - ERROR - load_data:65 - Failed to load data: [Errno 2] No such file or directory: 'data/cleaned/features_cleaned.csv'
2025-09-20 22:55:27,229 - BacktestRunner - ERROR - run_backtest:115 - Backtesting failed: [<PERSON>rrno 2] No such file or directory: 'data/cleaned/features_cleaned.csv'
2025-09-20 22:56:17,905 - BacktestRunner - INFO - __init__:44 - Initializing XAUUSD Backtesting System
2025-09-20 22:56:17,905 - BacktestRunner - INFO - load_data:49 - Loading data from data/cleaned
2025-09-20 22:56:18,092 - Backtest<PERSON>unner - INFO - load_data:56 - Data loaded successfully:
2025-09-20 22:56:18,092 - BacktestRunner - INFO - load_data:57 -   Features: (2957, 240)
2025-09-20 22:56:18,093 - BacktestRunner - INFO - load_data:58 -   Labels: (2957, 7)
2025-09-20 22:56:18,093 - BacktestRunner - INFO - load_data:59 -   OHLCV: (2957, 8)
2025-09-20 22:56:18,094 - BacktestRunner - INFO - load_data:60 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:56:18,094 - BacktestRunner - INFO - load_model:71 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 22:56:18,094 - BacktestRunner - ERROR - load_model:76 - Failed to load model: load_shape_consistent_ensemble() takes 0 positional arguments but 1 was given
2025-09-20 22:56:18,094 - BacktestRunner - ERROR - run_backtest:115 - Backtesting failed: load_shape_consistent_ensemble() takes 0 positional arguments but 1 was given
2025-09-20 22:56:58,667 - BacktestRunner - INFO - __init__:44 - Initializing XAUUSD Backtesting System
2025-09-20 22:56:58,668 - BacktestRunner - INFO - load_data:49 - Loading data from data/cleaned
2025-09-20 22:56:58,898 - BacktestRunner - INFO - load_data:56 - Data loaded successfully:
2025-09-20 22:56:58,898 - BacktestRunner - INFO - load_data:57 -   Features: (2957, 240)
2025-09-20 22:56:58,899 - BacktestRunner - INFO - load_data:58 -   Labels: (2957, 7)
2025-09-20 22:56:58,899 - BacktestRunner - INFO - load_data:59 -   OHLCV: (2957, 8)
2025-09-20 22:56:58,899 - BacktestRunner - INFO - load_data:60 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:56:58,900 - BacktestRunner - INFO - load_model:71 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 22:56:59,544 - BacktestRunner - INFO - load_model:74 - Ensemble model loaded successfully
2025-09-20 22:56:59,544 - BacktestRunner - ERROR - run_backtest:116 - Backtesting failed: BacktestConfig.__init__() missing 1 required positional argument: 'base_config'
2025-09-20 22:57:56,570 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-20 22:57:56,570 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-20 22:57:56,786 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-20 22:57:56,786 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-20 22:57:56,787 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-20 22:57:56,787 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-20 22:57:56,788 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:57:56,788 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 22:57:57,388 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-20 22:57:57,409 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-20 22:57:57,410 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-20 22:57:57,411 - BacktestRunner - ERROR - run_backtest:118 - Backtesting failed: BacktestFactory.__init__() missing 1 required positional argument: 'config'
2025-09-20 22:58:35,339 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-20 22:58:35,340 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-20 22:58:35,574 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-20 22:58:35,575 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-20 22:58:35,575 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-20 22:58:35,575 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-20 22:58:35,576 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:58:35,576 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 22:58:36,211 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-20 22:58:36,234 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-20 22:58:36,235 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-20 22:58:36,237 - BacktestRunner - ERROR - run_backtest:119 - Backtesting failed: BacktestFactory.create_backtester() takes 2 positional arguments but 4 were given
2025-09-20 22:59:15,486 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-20 22:59:15,486 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-20 22:59:15,723 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-20 22:59:15,723 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-20 22:59:15,723 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-20 22:59:15,724 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-20 22:59:15,724 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 22:59:15,724 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 22:59:16,371 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-20 22:59:16,394 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-20 22:59:16,395 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-20 22:59:16,396 - BacktestRunner - ERROR - run_backtest:119 - Backtesting failed: unhashable type: 'BacktestConfig'
2025-09-20 23:00:09,544 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-20 23:00:09,544 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-20 23:00:09,779 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-20 23:00:09,779 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-20 23:00:09,780 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-20 23:00:09,780 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-20 23:00:09,781 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-20 23:00:09,781 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-20 23:00:10,517 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-20 23:00:10,537 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-20 23:00:10,538 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-20 23:00:10,543 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-20 23:00:10,547 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.00 seconds
2025-09-20 23:00:10,548 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
2025-09-21 00:53:33,531 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 00:53:33,531 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 00:53:33,746 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 00:53:33,748 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 00:53:33,751 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 00:53:33,751 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 00:53:33,752 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:53:33,752 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 00:53:34,045 - BacktestRunner - ERROR - load_model:78 - Failed to load model: No module named 'catboost'
2025-09-21 00:53:34,046 - BacktestRunner - ERROR - run_backtest:121 - Backtesting failed: No module named 'catboost'
2025-09-21 00:53:52,596 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 00:53:52,597 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 00:53:52,796 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 00:53:52,796 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 00:53:52,796 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 00:53:52,796 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 00:53:52,797 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:53:52,797 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 00:53:56,067 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-21 00:53:56,149 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-21 00:53:56,150 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-21 00:53:56,152 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-21 00:53:56,155 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.00 seconds
2025-09-21 00:53:56,155 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
2025-09-21 00:54:51,570 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 00:54:51,571 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 00:54:51,764 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 00:54:51,765 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 00:54:51,765 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 00:54:51,765 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 00:54:51,766 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:54:51,766 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 00:54:52,264 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-21 00:54:52,327 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-21 00:54:52,327 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-21 00:54:52,330 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-21 00:54:52,651 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.32 seconds
2025-09-21 00:54:52,652 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
2025-09-21 00:57:41,438 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 00:57:41,439 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 00:57:41,618 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 00:57:41,619 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 00:57:41,619 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 00:57:41,619 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 00:57:41,619 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 00:57:41,620 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 00:57:42,099 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-21 00:57:42,183 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-21 00:57:42,183 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-21 00:57:42,187 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-21 00:57:42,512 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.33 seconds
2025-09-21 00:57:42,513 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
2025-09-21 01:01:10,927 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 01:01:10,929 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 01:01:11,132 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 01:01:11,132 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 01:01:11,132 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 01:01:11,133 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 01:01:11,133 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 01:01:11,133 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 01:01:11,707 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-21 01:01:11,797 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-21 01:01:11,797 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-21 01:01:11,801 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-21 01:01:12,212 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.41 seconds
2025-09-21 01:01:12,213 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
2025-09-21 01:02:06,502 - BacktestRunner - INFO - __init__:45 - Initializing XAUUSD Backtesting System
2025-09-21 01:02:06,503 - BacktestRunner - INFO - load_data:50 - Loading data from data/cleaned
2025-09-21 01:02:06,680 - BacktestRunner - INFO - load_data:57 - Data loaded successfully:
2025-09-21 01:02:06,681 - BacktestRunner - INFO - load_data:58 -   Features: (2957, 240)
2025-09-21 01:02:06,682 - BacktestRunner - INFO - load_data:59 -   Labels: (2957, 7)
2025-09-21 01:02:06,682 - BacktestRunner - INFO - load_data:60 -   OHLCV: (2957, 8)
2025-09-21 01:02:06,683 - BacktestRunner - INFO - load_data:61 -   Date range: 2025-06-23 01:00:00 to 2025-07-07 21:35:00
2025-09-21 01:02:06,683 - BacktestRunner - INFO - load_model:72 - Loading ensemble model from data/models/shape_consistent_ensemble_20250920_221743.pkl
2025-09-21 01:02:07,152 - BacktestRunner - INFO - load_model:75 - Ensemble model loaded successfully
2025-09-21 01:02:07,235 - BacktestRunner - INFO - run_backtest:96 - Using default configuration
2025-09-21 01:02:07,236 - BacktestRunner - INFO - run_backtest:99 - Initializing backtesting system...
2025-09-21 01:02:07,238 - BacktestRunner - INFO - run_backtest:105 - Starting backtesting analysis...
2025-09-21 01:02:07,584 - BacktestRunner - INFO - run_backtest:113 - Backtesting completed in 0.35 seconds
2025-09-21 01:02:07,585 - BacktestRunner - INFO - save_results:143 - Results saved to backtest/results
