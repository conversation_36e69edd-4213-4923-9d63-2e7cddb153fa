/**
 * Dashboard JavaScript
 *
 * Handles WebSocket connections, real-time updates, and UI interactions
 * for the XAUUSD AI Trading Dashboard.
 */

class Dashboard {
    constructor() {
        this.websocket = new DashboardWebSocket();
        this.charts = {};
        this.init();
    }

    init() {
        console.log('Dashboard initialized');
        // Initialize charts and UI components
        this.initializeCharts();
        this.setupEventListeners();
    }

    initializeCharts() {
        // Initialize Chart.js charts
        this.initializePriceChart();
    }

    setupEventListeners() {
        // Setup UI event listeners
        console.log('Event listeners setup complete');
    }

    updateMarketData(marketData) {
        // Update current price display with professional formatting
        const priceElement = document.getElementById('current-price');
        if (priceElement && marketData.price) {
            priceElement.textContent = `$${marketData.price.toFixed(2)}`;
            // Add subtle animation for price updates
            priceElement.style.transform = 'scale(1.05)';
            setTimeout(() => {
                priceElement.style.transform = 'scale(1)';
            }, 200);
        }
    }

    updateMetrics(metrics) {
        // Update P&L element
        const pnlElement = document.getElementById('daily-pnl');
        if (pnlElement && metrics.daily_pnl !== undefined) {
            const pnl = metrics.daily_pnl;
            pnlElement.textContent = `${pnl >= 0 ? '+' : ''}$${pnl.toFixed(2)}`;
            pnlElement.className = pnl >= 0 ? 'metric-value text-profit' : 'metric-value text-loss';
        }

        // Update win rate
        const winRateElement = document.getElementById('win-rate');
        if (winRateElement && metrics.win_rate !== undefined) {
            winRateElement.textContent = `${(metrics.win_rate * 100).toFixed(1)}%`;
        }

        // Update model confidence bars
        if (metrics.model_consensus) {
            this.updateModelConsensus(metrics.model_consensus);
        }
    }

    updateModelConsensus(consensus) {
        const models = ['lightgbm', 'catboost', 'xgboost', 'ensemble'];

        models.forEach(model => {
            const confidenceElement = document.getElementById(`${model}-confidence`);
            const barElement = document.getElementById(`${model}-bar`);

            if (confidenceElement && barElement && consensus[model] !== undefined) {
                const confidence = consensus[model] * 100;
                confidenceElement.textContent = `${confidence.toFixed(1)}%`;
                barElement.style.width = `${confidence}%`;
            }
        });
    }

    initializePriceChart() {
        const ctx = document.getElementById('priceChart');
        if (ctx) {
            this.charts.priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'XAUUSD Price',
                        data: [],
                        borderColor: '#FFD700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }
    }
}

class DashboardWebSocket {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnected = false;
        
        this.init();
    }
    
    init() {
        this.connect();
        this.setupEventHandlers();
    }
    
    connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = (event) => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.updateConnectionStatus(true);
                
                // Send initial ping
                this.sendMessage({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };
            
            this.ws.onclose = (event) => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateConnectionStatus(false);
                
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    setTimeout(() => {
                        this.reconnectAttempts++;
                        console.log(`Reconnection attempt ${this.reconnectAttempts}`);
                        this.connect();
                    }, this.reconnectDelay * this.reconnectAttempts);
                }
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('Error creating WebSocket connection:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    sendMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    handleMessage(data) {
        switch (data.type) {
            case 'pong':
                // Handle ping response
                break;
                
            case 'initial_data':
                this.handleInitialData(data);
                break;
                
            case 'update':
                this.handleUpdate(data);
                break;
                
            case 'data_response':
                this.handleDataResponse(data);
                break;
                
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    handleInitialData(data) {
        console.log('Received initial data:', data);
        // Update UI with initial data
        if (data.config) {
            document.title = data.config.title || 'Trading Dashboard';
        }
    }
    
    handleUpdate(data) {
        console.log('Received update:', data);
        
        // Update various dashboard components based on data type
        if (data.metrics) {
            this.updateMetrics(data.metrics);
        }
        
        if (data.positions) {
            this.updatePositions(data.positions);
        }
        
        if (data.signals) {
            this.updateSignals(data.signals);
        }
        
        if (data.system_status) {
            this.updateSystemStatus(data.system_status);
        }
        
        if (data.price_data) {
            this.updatePriceChart(data.price_data);
        }
    }
    
    handleDataResponse(data) {
        console.log('Received data response:', data);
        // Handle specific data requests
    }
    
    updateConnectionStatus(connected) {
        const statusIcon = document.getElementById('connection-status');
        const statusText = document.getElementById('connection-text');
        
        if (statusIcon && statusText) {
            if (connected) {
                statusIcon.className = 'fas fa-circle text-success me-1';
                statusText.textContent = 'Connected';
            } else {
                statusIcon.className = 'fas fa-circle text-danger me-1';
                statusText.textContent = 'Disconnected';
            }
        }
    }
    
    updateMetrics(metrics) {
        // Update account balance
        const balanceElement = document.getElementById('account-balance');
        if (balanceElement && metrics.account_balance !== undefined) {
            balanceElement.textContent = `$${metrics.account_balance.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })}`;
        }
        
        // Update total P&L
        const pnlElement = document.getElementById('total-pnl');
        if (pnlElement && metrics.total_pnl !== undefined) {
            const pnl = metrics.total_pnl;
            pnlElement.textContent = `$${pnl.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })}`;
            
            // Update color based on profit/loss
            pnlElement.className = pnl >= 0 ? 'text-success' : 'text-danger';
        }

        // Update win rate
        const winRateElement = document.getElementById('win-rate');
        if (winRateElement && metrics.win_rate !== undefined) {
            winRateElement.textContent = `${metrics.win_rate.toFixed(1)}%`;
        }

        // Update total trades
        const tradesElement = document.getElementById('total-trades');
        if (tradesElement && metrics.total_trades !== undefined) {
            tradesElement.textContent = metrics.total_trades.toString();
        }

        // Update Sharpe ratio
        const sharpeElement = document.getElementById('sharpe-ratio');
        if (sharpeElement && metrics.sharpe_ratio !== undefined) {
            sharpeElement.textContent = metrics.sharpe_ratio.toFixed(2);
        }
    }

    updatePositions(positions) {
        const tbody = document.querySelector('#positions-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (!positions || positions.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="6" class="text-center text-muted">No open positions</td>';
            tbody.appendChild(row);
            return;
        }

        positions.forEach(position => {
            const row = document.createElement('tr');
            const profitClass = position.profit >= 0 ? 'text-success' : 'text-danger';
            const typeClass = position.type === 'BUY' ? 'success' : 'danger';

            row.innerHTML = `
                <td>${position.symbol}</td>
                <td><span class="badge bg-${typeClass}">${position.type}</span></td>
                <td>${position.volume}</td>
                <td>$${position.open_price.toFixed(2)}</td>
                <td>$${position.current_price.toFixed(2)}</td>
                <td class="${profitClass}">$${position.profit.toFixed(2)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    updateSignals(signals) {
        const tbody = document.querySelector('#signals-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (!signals || signals.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="text-center text-muted">No recent signals</td>';
            tbody.appendChild(row);
            return;
        }

        // Keep only the last 10 signals
        const recentSignals = signals.slice(-10);

        recentSignals.forEach(signal => {
            const row = document.createElement('tr');
            const directionClass = signal.direction === 'BUY' ? 'success' : 'danger';

            row.innerHTML = `
                <td>${new Date(signal.timestamp).toLocaleTimeString()}</td>
                <td>${signal.symbol}</td>
                <td><span class="badge bg-${directionClass}">${signal.direction}</span></td>
                <td>${(signal.confidence * 100).toFixed(1)}%</td>
                <td>$${signal.price.toFixed(2)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    updateSystemStatus(status) {
        // Update system status badge
        const statusBadge = document.getElementById('system-status-badge');
        if (statusBadge && status.status) {
            const badgeClass = status.status === 'running' ? 'bg-success' : 'bg-danger';
            statusBadge.className = `badge ${badgeClass}`;
            statusBadge.textContent = status.status.toUpperCase();
        }

        // Update CPU usage
        const cpuElement = document.getElementById('cpu-usage');
        if (cpuElement && status.cpu_usage !== undefined) {
            cpuElement.textContent = `${status.cpu_usage.toFixed(1)}%`;
        }

        // Update memory usage
        const memoryElement = document.getElementById('memory-usage');
        if (memoryElement && status.memory_usage !== undefined) {
            memoryElement.textContent = `${status.memory_usage.toFixed(1)}%`;
        }

        // Update uptime
        const uptimeElement = document.getElementById('uptime');
        if (uptimeElement && status.uptime) {
            uptimeElement.textContent = status.uptime;
        }
    }

    updatePriceChart(priceData) {
        if (!window.priceChart || !priceData) return;

        const chart = window.priceChart;

        // Update chart with new data points
        const labels = priceData.map(point => new Date(point.timestamp).toLocaleTimeString());
        const prices = priceData.map(point => point.close);

        // Keep only last 50 data points for performance
        if (labels.length > 50) {
            labels.splice(0, labels.length - 50);
            prices.splice(0, prices.length - 50);
        }

        chart.data.labels = labels;
        chart.data.datasets[0].data = prices;
        chart.update('none'); // No animation for real-time updates
    }

    setupEventHandlers() {
        // Add event handlers for UI interactions
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize charts if Chart.js is available
            if (typeof Chart !== 'undefined') {
                this.initializeCharts();
            }

            // Setup refresh buttons
            const refreshButtons = document.querySelectorAll('[data-refresh]');
            refreshButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const dataType = e.target.getAttribute('data-refresh');
                    this.requestData(dataType);
                });
            });
        });
    }

    requestData(dataType) {
        this.sendMessage({
            type: 'request_data',
            data_type: dataType,
            timestamp: new Date().toISOString()
        });
    }

    initializeCharts() {
        // Initialize price chart
        const priceChartCanvas = document.getElementById('price-chart');
        if (priceChartCanvas) {
            const ctx = priceChartCanvas.getContext('2d');
            window.priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'XAUUSD Price',
                        data: [],
                        borderColor: '#00d4aa',
                        backgroundColor: 'rgba(0, 212, 170, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }
    }

    updateMetrics(metrics) {
        // Update P&L element
        const pnlElement = document.getElementById('daily-pnl');
        if (pnlElement && metrics.daily_pnl !== undefined) {
            const pnl = metrics.daily_pnl;
            pnlElement.textContent = `${pnl >= 0 ? '+' : ''}$${pnl.toFixed(2)}`;
            pnlElement.className = pnl >= 0 ? 'metric-value text-profit' : 'metric-value text-loss';
        }

        // Update win rate
        const winRateElement = document.getElementById('win-rate');
        if (winRateElement && metrics.win_rate !== undefined) {
            winRateElement.textContent = `${(metrics.win_rate * 100).toFixed(1)}%`;
        }
        
        // Update model confidence bars
        if (metrics.model_consensus) {
            this.updateModelConsensus(metrics.model_consensus);
        }
    }
    
    updateModelConsensus(consensus) {
        const models = ['lightgbm', 'catboost', 'xgboost', 'ensemble'];
        
        models.forEach(model => {
            const confidenceElement = document.getElementById(`${model}-confidence`);
            const barElement = document.getElementById(`${model}-bar`);
            
            if (confidenceElement && barElement && consensus[model] !== undefined) {
                const confidence = consensus[model] * 100;
                confidenceElement.textContent = `${confidence.toFixed(1)}%`;
                barElement.style.width = `${confidence}%`;
            }
        });
    }
    
    updatePositions(positions) {
        const tableBody = document.querySelector('#positions-table tbody');
        if (!tableBody) return;
        
        if (positions.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No open positions</td></tr>';
            return;
        }
        
        tableBody.innerHTML = positions.map(position => `
            <tr>
                <td>${position.symbol}</td>
                <td>
                    <span class="badge ${position.direction === 'long' ? 'bg-success' : 'bg-danger'}">
                        ${position.direction.toUpperCase()}
                    </span>
                </td>
                <td>${position.size}</td>
                <td>${position.entry_price.toFixed(2)}</td>
                <td>${position.current_price.toFixed(2)}</td>
                <td class="${position.unrealized_pnl >= 0 ? 'text-success' : 'text-danger'}">
                    $${position.unrealized_pnl.toFixed(2)}
                </td>
            </tr>
        `).join('');
    }
    
    updateSignals(signals) {
        const tableBody = document.querySelector('#signals-table tbody');
        if (!tableBody) return;
        
        if (signals.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No recent signals</td></tr>';
            return;
        }
        
        tableBody.innerHTML = signals.slice(0, 10).map(signal => `
            <tr>
                <td>${new Date(signal.timestamp).toLocaleTimeString()}</td>
                <td>
                    <span class="badge ${this.getSignalBadgeClass(signal.signal_type)}">
                        ${signal.signal_type.toUpperCase()}
                    </span>
                </td>
                <td>${(signal.confidence * 100).toFixed(1)}%</td>
                <td>${signal.entry_price.toFixed(2)}</td>
                <td>
                    <span class="badge ${this.getStatusBadgeClass(signal.executed, signal.outcome)}">
                        ${signal.executed ? (signal.outcome || 'Pending') : 'Not Executed'}
                    </span>
                </td>
            </tr>
        `).join('');
    }
    
    getSignalBadgeClass(signalType) {
        switch (signalType.toLowerCase()) {
            case 'buy':
            case 'long':
                return 'bg-success';
            case 'sell':
            case 'short':
                return 'bg-danger';
            default:
                return 'bg-warning';
        }
    }
    
    getStatusBadgeClass(executed, outcome) {
        if (!executed) return 'bg-secondary';
        
        switch (outcome?.toLowerCase()) {
            case 'win':
                return 'bg-success';
            case 'loss':
                return 'bg-danger';
            default:
                return 'bg-warning';
        }
    }
    
    updateSystemStatus(status) {
        // Update system status indicator
        const systemStatusElement = document.getElementById('system-status');
        if (systemStatusElement) {
            systemStatusElement.textContent = status.status || 'Unknown';
        }
        
        // Update component status indicators
        const components = ['mt5', 'models', 'data'];
        components.forEach(component => {
            const statusElement = document.getElementById(`${component}-status`);
            if (statusElement && status[`${component}_connected`] !== undefined) {
                const isConnected = status[`${component}_connected`];
                statusElement.innerHTML = isConnected 
                    ? '<i class="fas fa-check-circle"></i>'
                    : '<i class="fas fa-times-circle"></i>';
                statusElement.className = `h4 mb-0 ${isConnected ? 'text-success' : 'text-danger'}`;
            }
        });
        
        // Update last update times
        if (status.last_signal_time) {
            const lastSignalElement = document.getElementById('last-signal-time');
            if (lastSignalElement) {
                lastSignalElement.textContent = new Date(status.last_signal_time).toLocaleTimeString();
            }
        }
        
        if (status.last_data_update) {
            const lastDataElement = document.getElementById('last-data-update');
            if (lastDataElement) {
                lastDataElement.textContent = new Date(status.last_data_update).toLocaleTimeString();
            }
        }
    }
    
    updatePriceChart(priceData) {
        if (window.priceChart && priceData.length > 0) {
            const labels = priceData.map(point => new Date(point.timestamp));
            const prices = priceData.map(point => point.close);
            
            window.priceChart.data.labels = labels;
            window.priceChart.data.datasets[0].data = prices;
            window.priceChart.update('none');
        }
    }
    
    setupEventHandlers() {
        // Setup periodic ping to keep connection alive
        setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000); // Ping every 30 seconds
        
        // Setup data refresh button handlers
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-refresh]')) {
                const dataType = event.target.getAttribute('data-refresh');
                this.requestData(dataType);
            }
        });
        
        // Setup timeframe button handlers
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-timeframe]')) {
                const timeframe = event.target.getAttribute('data-timeframe');
                this.changeTimeframe(timeframe);
                
                // Update active button
                document.querySelectorAll('[data-timeframe]').forEach(btn => {
                    btn.classList.remove('active');
                });
                event.target.classList.add('active');
            }
        });
    }
    
    requestData(dataType) {
        this.sendMessage({
            type: 'request_data',
            data_type: dataType,
            timestamp: new Date().toISOString()
        });
    }
    
    changeTimeframe(timeframe) {
        this.sendMessage({
            type: 'change_timeframe',
            timeframe: timeframe,
            timestamp: new Date().toISOString()
        });
    }
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function formatPercentage(value, decimals = 1) {
    return `${(value * 100).toFixed(decimals)}%`;
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}

function formatDate(timestamp) {
    return new Date(timestamp).toLocaleDateString();
}

// Initialize Dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the dashboard
    if (typeof Dashboard !== 'undefined') {
        window.dashboardInstance = new Dashboard();
    } else {
        console.error('Dashboard class not found');
    }
});

// End of Dashboard JavaScript
