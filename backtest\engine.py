"""
Backtesting Engine Implementation

Core backtesting engines for different testing strategies including
walk-forward validation, single period testing, and ensemble comparison.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import sys
from pathlib import Path
import joblib
import time

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin

from .base import BaseBacktester, BacktestResult, Trade
from .config import BacktestConfig
from .trading_logic import ModelDrivenTradingLogic
from .performance import PerformanceAnalyzer


class WalkForwardBacktester(BaseBacktester):
    """
    Walk-forward backtesting implementation.
    
    Implements walk-forward validation with multiple time periods,
    realistic trading conditions, and comprehensive performance analysis.
    """
    
    def __init__(self, config: BacktestConfig, ensemble_model=None):
        """
        Initialize walk-forward backtester.
        
        Args:
            config: Backtesting configuration
            ensemble_model: Trained ensemble model
        """
        super().__init__(config)
        self.ensemble_model = ensemble_model
        self.trading_logic = ModelDrivenTradingLogic(config)
        self.performance_analyzer = PerformanceAnalyzer(config)
        
        # Walk-forward specific state
        self.walk_forward_results = []
        self.training_windows = []
        self.testing_windows = []
        
        self.logger.info("Walk-forward backtester initialized")
    
    def run_backtest(self, data: pd.DataFrame, features: pd.DataFrame,
                    labels: pd.DataFrame, **kwargs) -> BacktestResult:
        """
        Run walk-forward backtesting.
        
        Args:
            data: Historical OHLCV data
            features: Engineered features
            labels: Model labels/predictions
            **kwargs: Additional parameters
            
        Returns:
            BacktestResult with comprehensive results
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        # Validate input data
        validation_errors = self.validate_data(data, features)
        if validation_errors:
            errors.extend(validation_errors)
            return self._create_empty_result(errors, warnings, time.time() - start_time)
        
        try:
            self.logger.info("Starting walk-forward backtesting...")
            
            # Generate walk-forward windows
            windows = self._generate_walk_forward_windows(data.index)
            self.logger.info(f"Generated {len(windows)} walk-forward windows")
            
            # Process each window
            all_trades = []
            window_results = []
            
            for i, (train_start, train_end, test_start, test_end) in enumerate(windows):
                self.logger.info(f"Processing window {i+1}/{len(windows)}: "
                               f"Train: {train_start} to {train_end}, "
                               f"Test: {test_start} to {test_end}")
                
                # Extract window data
                train_data = data.loc[train_start:train_end]
                train_features = features.loc[train_start:train_end]
                train_labels = labels.loc[train_start:train_end]
                
                test_data = data.loc[test_start:test_end]
                test_features = features.loc[test_start:test_end]
                test_labels = labels.loc[test_start:test_end]
                
                # Run backtest on test window
                window_trades = self._run_window_backtest(
                    test_data, test_features, test_labels,
                    train_data, train_features, train_labels
                )
                
                all_trades.extend(window_trades)
                window_results.append({
                    'window': i + 1,
                    'train_period': (train_start, train_end),
                    'test_period': (test_start, test_end),
                    'trades': len(window_trades),
                    'pnl': sum(trade.net_pnl for trade in window_trades)
                })
                
                self.logger.info(f"Window {i+1} completed: {len(window_trades)} trades, "
                               f"PnL: ${sum(trade.net_pnl for trade in window_trades):.2f}")
            
            # Store results
            self.trades = all_trades
            self.walk_forward_results = window_results
            
            # Calculate performance metrics
            performance_metrics = self.performance_analyzer.calculate_comprehensive_metrics(
                self.trades, self.equity_curve, self.config.initial_capital
            )
            
            # Create equity and drawdown curves
            equity_df = pd.DataFrame(self.equity_curve)
            drawdown_df = pd.DataFrame(self.drawdown_curve)
            
            # Calculate monthly returns
            monthly_returns = self.performance_analyzer.calculate_monthly_returns(equity_df)
            
            # Analyze trades
            trade_analysis = self.performance_analyzer.analyze_trades(self.trades)
            
            # Model-specific analysis
            model_analysis = self._analyze_model_performance()
            
            execution_time = time.time() - start_time
            self.logger.info(f"Walk-forward backtesting completed in {execution_time:.2f}s")
            
            return BacktestResult(
                trades=self.trades,
                performance_metrics=performance_metrics,
                equity_curve=equity_df,
                drawdown_curve=drawdown_df,
                monthly_returns=monthly_returns,
                trade_analysis=trade_analysis,
                model_analysis=model_analysis,
                errors=errors,
                warnings=warnings,
                execution_time=execution_time,
                metadata={
                    'backtest_type': 'walk_forward',
                    'total_windows': len(windows),
                    'window_results': window_results,
                    'config': self.config.__dict__
                }
            )
            
        except Exception as e:
            error_msg = f"Walk-forward backtesting failed: {str(e)}"
            self.logger.error(error_msg)
            errors.append(error_msg)
            return self._create_empty_result(errors, warnings, time.time() - start_time)
    
    def _generate_walk_forward_windows(self, date_index: pd.DatetimeIndex) -> List[Tuple]:
        """
        Generate walk-forward validation windows.

        Args:
            date_index: DateTime index of the data

        Returns:
            List of (train_start, train_end, test_start, test_end) tuples
        """
        windows = []

        start_date = date_index[0]
        end_date = date_index[-1]
        total_days = (end_date - start_date).days

        self.logger.info(f"Data range: {start_date} to {end_date}")
        self.logger.info(f"Total data span: {total_days} days")

        # Adaptive window sizing based on available data
        if total_days < 30:
            # Very limited data - use simple train/test split
            training_days = max(3, int(total_days * 0.6))  # 60% for training, minimum 3 days
            testing_days = max(2, total_days - training_days)  # Remaining for testing, minimum 2 days
            step_days = max(1, int(testing_days / 2))  # Step by half the test window
        elif total_days < 90:
            # Limited data - use smaller windows
            training_days = max(7, int(total_days * 0.5))  # 50% for training, minimum 1 week
            testing_days = max(3, int(total_days * 0.3))   # 30% for testing, minimum 3 days
            step_days = max(2, int(testing_days / 2))      # Step by half the test window
        else:
            # Sufficient data - use configured windows
            training_days = self.config.walkforward_config['training_window_months'] * 30
            testing_days = self.config.walkforward_config['testing_window_months'] * 30
            step_days = self.config.walkforward_config['step_size_months'] * 30

        self.logger.info(f"Training days: {training_days}, Testing days: {testing_days}, Step days: {step_days}")

        current_date = start_date

        while current_date < end_date:
            # Training window
            train_start = current_date
            train_end = train_start + timedelta(days=training_days)

            # Testing window
            test_start = train_end + timedelta(hours=1)  # Small gap
            test_end = test_start + timedelta(days=testing_days)

            # Check if we have enough data
            if test_end > end_date:
                # For the last window, use all remaining data for testing
                test_end = end_date
                if (test_end - test_start).days < 1:  # Need at least 1 day for testing
                    break

            # Ensure we have minimum training samples
            train_mask = (date_index >= train_start) & (date_index <= train_end)
            train_samples = len(date_index[train_mask])

            if train_samples < self.config.walkforward_config['min_training_samples']:
                self.logger.debug(f"Insufficient training samples: {train_samples} < {self.config.walkforward_config['min_training_samples']}")
                current_date += timedelta(days=step_days)
                continue

            # Ensure we have test samples
            test_mask = (date_index >= test_start) & (date_index <= test_end)
            test_samples = len(date_index[test_mask])

            if test_samples < 10:  # Minimum test samples
                self.logger.debug(f"Insufficient test samples: {test_samples} < 10")
                current_date += timedelta(days=step_days)
                continue

            self.logger.info(f"Created window: Train {train_start} to {train_end} ({train_samples} samples), Test {test_start} to {test_end} ({test_samples} samples)")
            windows.append((train_start, train_end, test_start, test_end))

            # Move to next window
            current_date += timedelta(days=step_days)

        return windows
    
    def _run_window_backtest(self, test_data: pd.DataFrame, test_features: pd.DataFrame,
                           test_labels: pd.DataFrame, train_data: pd.DataFrame,
                           train_features: pd.DataFrame, train_labels: pd.DataFrame) -> List[Trade]:
        """
        Run backtesting on a single window.
        
        Args:
            test_data: Test period OHLCV data
            test_features: Test period features
            test_labels: Test period labels
            train_data: Training period OHLCV data (for context)
            train_features: Training period features (for context)
            train_labels: Training period labels (for context)
            
        Returns:
            List of trades executed in this window
        """
        window_trades = []
        current_position = None
        
        for i, (timestamp, row) in enumerate(test_data.iterrows()):
            try:
                # Get current features and labels
                current_features = test_features.loc[timestamp]
                current_labels = test_labels.loc[timestamp] if timestamp in test_labels.index else None
                
                # Generate model signal
                if self.ensemble_model and current_labels is not None:
                    signal = self._generate_model_signal(current_features, current_labels, row)
                    
                    # Check for entry signal
                    if current_position is None and signal['should_enter']:
                        self.logger.debug(f"Entry signal generated at {timestamp}: {signal}")
                        trade = self.execute_trade(signal, row, current_features)
                        if trade:
                            current_position = trade
                            window_trades.append(trade)
                            self.logger.info(f"Trade executed at {timestamp}: {trade.direction} {trade.size} lots")
                        else:
                            self.logger.warning(f"Failed to execute trade at {timestamp}")
                    
                    # Check for exit signal
                    elif current_position is not None:
                        should_exit = self.update_position(row, current_features)
                        if should_exit:
                            current_position.exit_time = timestamp
                            current_position.exit_price = row['close']
                            current_position = self._finalize_trade(current_position, row)
                            current_position = None
                
            except Exception as e:
                self.logger.warning(f"Error processing timestamp {timestamp}: {str(e)}")
                continue
        
        # Close any remaining position at the end of the window
        if current_position is not None:
            current_position.exit_time = test_data.index[-1]
            current_position.exit_price = test_data.iloc[-1]['close']
            current_position = self._finalize_trade(current_position, test_data.iloc[-1])
        
        return window_trades
    
    def _generate_model_signal(self, features: pd.Series, labels: pd.Series, 
                             current_data: pd.Series) -> Dict[str, Any]:
        """
        Generate trading signal using ensemble model.
        
        Args:
            features: Current features
            labels: Current labels
            current_data: Current OHLCV data
            
        Returns:
            Dictionary with signal information
        """
        try:
            # Prepare features for model prediction
            feature_array = features.values.reshape(1, -1)
            
            # Get model prediction
            if self.ensemble_model:
                prediction = self.ensemble_model.predict(feature_array)
                if hasattr(prediction, 'values'):
                    pred_values = prediction.values[0]
                else:
                    pred_values = prediction[0]
            else:
                # Use labels as proxy if no model available
                pred_values = labels.values
            
            # Extract prediction components (assuming standard format)
            signal_prob = pred_values[0] if len(pred_values) > 0 else 0.5
            tp1_distance = pred_values[1] if len(pred_values) > 1 else 15.0
            tp2_distance = pred_values[2] if len(pred_values) > 2 else 40.0
            sl_distance = pred_values[3] if len(pred_values) > 3 else 25.0
            
            # Apply trading logic
            signal = self.trading_logic.generate_signal(
                signal_prob, tp1_distance, tp2_distance, sl_distance,
                current_data, features
            )
            
            return signal
            
        except Exception as e:
            self.logger.warning(f"Error generating model signal: {str(e)}")
            return {'should_enter': False, 'confidence': 0.0}
    
    def execute_trade(self, signal: Dict[str, Any], current_data: pd.Series,
                     features: pd.Series) -> Optional[Trade]:
        """
        Execute a trade based on model signal.
        
        Args:
            signal: Trading signal from model
            current_data: Current OHLCV data
            features: Current features
            
        Returns:
            Trade object if executed, None otherwise
        """
        if not signal.get('should_enter', False):
            return None
        
        try:
            # Calculate position size
            position_size = self.config.get_risk_adjusted_position_size(
                self.equity, signal.get('sl_distance', 25.0)
            )
            
            # Apply session multiplier
            session_multiplier = self.config.get_session_multiplier(current_data.name)
            position_size *= session_multiplier
            
            # Calculate transaction costs
            costs = self.calculate_transaction_costs(position_size, current_data['close'])
            
            # Create trade
            trade = Trade(
                entry_time=current_data.name,
                entry_price=current_data['close'],
                direction=signal.get('direction', 'long'),
                size=position_size,
                tp1_price=current_data['close'] + signal.get('tp1_distance', 15.0) * 0.01,
                tp2_price=current_data['close'] + signal.get('tp2_distance', 40.0) * 0.01,
                sl_price=current_data['close'] - signal.get('sl_distance', 25.0) * 0.01,
                model_confidence=signal.get('confidence', 0.0),
                model_consensus=signal.get('consensus', {}),
                entry_features=features.to_dict(),
                **costs
            )
            
            self.logger.debug(f"Executed trade: {trade.direction} {trade.size} lots at {trade.entry_price}")
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {str(e)}")
            return None
    
    def update_position(self, current_data: pd.Series, features: pd.Series) -> bool:
        """
        Update current position based on exit conditions.
        
        Args:
            current_data: Current OHLCV data
            features: Current features
            
        Returns:
            True if position should be closed, False otherwise
        """
        # Implement exit logic based on TP/SL levels and model signals
        # This is a simplified version - full implementation would be more complex
        return False  # Placeholder
    
    def _finalize_trade(self, trade: Trade, current_data: pd.Series) -> Trade:
        """
        Finalize trade by calculating P&L and updating statistics.
        
        Args:
            trade: Trade to finalize
            current_data: Current market data
            
        Returns:
            Finalized trade
        """
        # Calculate P&L
        if trade.direction == 'long':
            trade.pnl = (trade.exit_price - trade.entry_price) * trade.size * 100
        else:
            trade.pnl = (trade.entry_price - trade.exit_price) * trade.size * 100
        
        trade.pnl_pips = trade.pnl / (trade.size * self.config.pip_value)
        
        # Update equity curve
        self.update_equity_curve(trade.exit_time, trade.net_pnl)
        
        # Update statistics
        self.stats['trades_executed'] += 1
        self.stats['total_pnl'] += trade.net_pnl
        self.stats['total_costs'] += trade.total_cost
        
        if trade.is_winner:
            self.stats['winning_trades'] += 1
        else:
            self.stats['losing_trades'] += 1
        
        return trade
    
    def _analyze_model_performance(self) -> Dict[str, Any]:
        """Analyze model-specific performance metrics."""
        if not self.trades:
            return {}
        
        # Calculate model confidence vs performance correlation
        confidences = [trade.model_confidence for trade in self.trades]
        pnls = [trade.net_pnl for trade in self.trades]
        
        confidence_correlation = np.corrcoef(confidences, pnls)[0, 1] if len(confidences) > 1 else 0.0
        
        # Analyze performance by confidence buckets
        high_conf_trades = [t for t in self.trades if t.model_confidence > 0.8]
        med_conf_trades = [t for t in self.trades if 0.6 <= t.model_confidence <= 0.8]
        low_conf_trades = [t for t in self.trades if t.model_confidence < 0.6]
        
        return {
            'confidence_correlation': confidence_correlation,
            'avg_confidence': np.mean(confidences),
            'high_confidence_trades': len(high_conf_trades),
            'high_confidence_win_rate': sum(1 for t in high_conf_trades if t.is_winner) / max(1, len(high_conf_trades)),
            'medium_confidence_trades': len(med_conf_trades),
            'medium_confidence_win_rate': sum(1 for t in med_conf_trades if t.is_winner) / max(1, len(med_conf_trades)),
            'low_confidence_trades': len(low_conf_trades),
            'low_confidence_win_rate': sum(1 for t in low_conf_trades if t.is_winner) / max(1, len(low_conf_trades)),
            'walk_forward_windows': len(self.walk_forward_results),
            'avg_trades_per_window': np.mean([w['trades'] for w in self.walk_forward_results]),
            'avg_pnl_per_window': np.mean([w['pnl'] for w in self.walk_forward_results])
        }
    
    def _create_empty_result(self, errors: List[str], warnings: List[str], 
                           execution_time: float) -> BacktestResult:
        """Create empty result for error cases."""
        return BacktestResult(
            trades=[],
            performance_metrics={},
            equity_curve=pd.DataFrame(),
            drawdown_curve=pd.DataFrame(),
            monthly_returns=pd.DataFrame(),
            trade_analysis={},
            model_analysis={},
            errors=errors,
            warnings=warnings,
            execution_time=execution_time,
            metadata={'backtest_type': 'walk_forward', 'status': 'failed'}
        )


class SinglePeriodBacktester(BaseBacktester):
    """Single period backtesting implementation."""
    
    def __init__(self, config: BacktestConfig, ensemble_model=None):
        super().__init__(config)
        self.ensemble_model = ensemble_model
        self.trading_logic = ModelDrivenTradingLogic(config)
        self.performance_analyzer = PerformanceAnalyzer(config)
    
    def run_backtest(self, data: pd.DataFrame, features: pd.DataFrame,
                    labels: pd.DataFrame, **kwargs) -> BacktestResult:
        """Run single period backtesting."""
        # Implementation similar to walk-forward but for single period
        # This is a placeholder - full implementation would follow similar pattern
        return self._create_empty_result([], [], 0.0)
    
    def execute_trade(self, signal: Dict[str, Any], current_data: pd.Series,
                     features: pd.Series) -> Optional[Trade]:
        """Execute trade for single period testing."""
        return None
    
    def update_position(self, current_data: pd.Series, features: pd.Series) -> bool:
        """Update position for single period testing."""
        return False
    
    def _create_empty_result(self, errors: List[str], warnings: List[str], 
                           execution_time: float) -> BacktestResult:
        """Create empty result for error cases."""
        return BacktestResult(
            trades=[],
            performance_metrics={},
            equity_curve=pd.DataFrame(),
            drawdown_curve=pd.DataFrame(),
            monthly_returns=pd.DataFrame(),
            trade_analysis={},
            model_analysis={},
            errors=errors,
            warnings=warnings,
            execution_time=execution_time,
            metadata={'backtest_type': 'single_period', 'status': 'failed'}
        )


# Placeholder classes for other backtesting types
class MonteCarloBacktester(BaseBacktester):
    """Monte Carlo backtesting implementation."""
    pass

class CrossValidationBacktester(BaseBacktester):
    """Cross-validation backtesting implementation."""
    pass

class EnsembleComparisonBacktester(BaseBacktester):
    """Ensemble comparison backtesting implementation."""
    pass
