"""
Base Dashboard Components

Provides abstract base classes and data structures for the dashboard system
following the established factory pattern architecture.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
import pandas as pd

# Import logging from existing system
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin


class DashboardStatus(Enum):
    """Dashboard component status enumeration."""
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class DataSourceType(Enum):
    """Data source types for dashboard."""
    MT5_REALTIME = "mt5_realtime"
    BACKTEST_RESULTS = "backtest_results"
    FORWARD_TEST_RESULTS = "forward_test_results"
    MODEL_OUTPUTS = "model_outputs"
    ACCOUNT_DATA = "account_data"
    SYSTEM_METRICS = "system_metrics"


@dataclass
class DashboardMetrics:
    """Container for dashboard performance metrics."""
    timestamp: datetime
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    total_pnl: float = 0.0
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    account_balance: float = 0.0
    account_equity: float = 0.0
    margin_used: float = 0.0
    margin_free: float = 0.0
    open_positions: int = 0
    system_uptime_hours: float = 0.0
    last_signal_time: Optional[datetime] = None
    model_confidence_avg: float = 0.0
    
    @property
    def profit_factor(self) -> float:
        """Calculate profit factor."""
        if self.losing_trades == 0:
            return float('inf') if self.winning_trades > 0 else 0.0
        
        total_wins = self.winning_trades * abs(self.total_pnl / max(self.total_trades, 1))
        total_losses = self.losing_trades * abs(self.total_pnl / max(self.total_trades, 1))
        
        return total_wins / total_losses if total_losses > 0 else 0.0


@dataclass
class LivePosition:
    """Live trading position data structure."""
    position_id: str
    symbol: str
    direction: str  # 'long' or 'short'
    entry_time: datetime
    entry_price: float
    current_price: float
    size: float
    unrealized_pnl: float
    unrealized_pnl_pips: float
    tp_price: Optional[float] = None
    sl_price: Optional[float] = None
    model_confidence: float = 0.0
    duration_minutes: float = 0.0
    
    @property
    def is_profitable(self) -> bool:
        """Check if position is currently profitable."""
        return self.unrealized_pnl > 0


@dataclass
class RecentSignal:
    """Recent trading signal data structure."""
    timestamp: datetime
    signal_type: str  # 'buy', 'sell', 'hold'
    confidence: float
    entry_price: float
    tp_price: float
    sl_price: float
    model_consensus: Dict[str, float]
    executed: bool = False
    execution_time: Optional[datetime] = None
    execution_price: Optional[float] = None
    outcome: Optional[str] = None  # 'win', 'loss', 'pending'


@dataclass
class SystemHealth:
    """System health and status information."""
    timestamp: datetime
    mt5_connected: bool = False
    models_loaded: bool = False
    data_feed_active: bool = False
    last_data_update: Optional[datetime] = None
    last_model_prediction: Optional[datetime] = None
    system_errors: List[str] = field(default_factory=list)
    system_warnings: List[str] = field(default_factory=list)
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    
    @property
    def overall_status(self) -> DashboardStatus:
        """Determine overall system status."""
        if self.system_errors:
            return DashboardStatus.ERROR
        elif not (self.mt5_connected and self.models_loaded and self.data_feed_active):
            return DashboardStatus.INITIALIZING
        else:
            return DashboardStatus.RUNNING


class BaseDashboardComponent(ABC):
    """
    Abstract base class for all dashboard components.

    Provides common functionality and interface for dashboard components
    following the established factory pattern architecture.
    """

    def __init__(self, config: 'DashboardConfig'):
        """
        Initialize base dashboard component.

        Args:
            config: Dashboard configuration instance
        """
        self.config = config
        self.status = DashboardStatus.INITIALIZING
        self.last_update = None
        self.errors = []
        self.warnings = []

        # Initialize logging
        from data_collection.error_handling.logger import get_logger
        self.logger = get_logger(self.__class__.__name__)
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        Initialize the dashboard component.
        
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    def start(self) -> bool:
        """
        Start the dashboard component.
        
        Returns:
            True if start successful, False otherwise
        """
        pass
    
    @abstractmethod
    def stop(self) -> bool:
        """
        Stop the dashboard component.
        
        Returns:
            True if stop successful, False otherwise
        """
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """
        Get current component status.
        
        Returns:
            Dictionary containing status information
        """
        pass
    
    def add_error(self, error: str):
        """Add error to component error list."""
        self.errors.append({
            'timestamp': datetime.now(),
            'message': error
        })
        self.logger.error(error)
    
    def add_warning(self, warning: str):
        """Add warning to component warning list."""
        self.warnings.append({
            'timestamp': datetime.now(),
            'message': warning
        })
        self.logger.warning(warning)
    
    def clear_errors(self):
        """Clear component error list."""
        self.errors.clear()
    
    def clear_warnings(self):
        """Clear component warning list."""
        self.warnings.clear()


class BaseDashboardDataProvider(BaseDashboardComponent):
    """
    Abstract base class for dashboard data providers.
    
    Data providers are responsible for fetching and formatting data
    from various sources for dashboard consumption.
    """
    
    @abstractmethod
    def get_data(self, data_type: DataSourceType, **kwargs) -> Dict[str, Any]:
        """
        Get data from the provider.
        
        Args:
            data_type: Type of data to retrieve
            **kwargs: Additional parameters for data retrieval
            
        Returns:
            Dictionary containing requested data
        """
        pass
    
    @abstractmethod
    def is_data_available(self, data_type: DataSourceType) -> bool:
        """
        Check if data is available from this provider.
        
        Args:
            data_type: Type of data to check
            
        Returns:
            True if data is available, False otherwise
        """
        pass


class BaseDashboardRenderer(BaseDashboardComponent):
    """
    Abstract base class for dashboard renderers.
    
    Renderers are responsible for generating HTML, charts, and other
    visual components for the dashboard.
    """
    
    @abstractmethod
    def render_page(self, page_type: str, data: Dict[str, Any]) -> str:
        """
        Render a dashboard page.
        
        Args:
            page_type: Type of page to render
            data: Data to include in the page
            
        Returns:
            Rendered HTML content
        """
        pass
    
    @abstractmethod
    def render_component(self, component_type: str, data: Dict[str, Any]) -> str:
        """
        Render a dashboard component.
        
        Args:
            component_type: Type of component to render
            data: Data to include in the component
            
        Returns:
            Rendered HTML content
        """
        pass
