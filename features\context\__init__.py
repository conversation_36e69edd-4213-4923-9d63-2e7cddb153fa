"""
Market Context Features Module

Provides market context and regime features including:
- Trading session analysis
- Temporal features
- Economic calendar integration
- Market regime detection
"""

from .sessions import TradingSessionFeatures
from .temporal import TemporalPatternFeatures

# Note: EconomicCalendarFeatures not yet implemented
try:
    from .economic import EconomicCalendarFeatures
    ECONOMIC_FEATURES_AVAILABLE = True
except ImportError:
    ECONOMIC_FEATURES_AVAILABLE = False

__all__ = [
    'TradingSessionFeatures',
    'TemporalPatternFeatures'
]

if ECONOMIC_FEATURES_AVAILABLE:
    __all__.append('EconomicCalendarFeatures')
