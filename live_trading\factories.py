"""
Live Trading Factory System

Provides factory classes for creating live trading components following
clean architecture principles and enabling easy extensibility.
"""

from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from datetime import datetime

from .config import LiveTradingConfig
from .base import BaseLiveTrader, LiveTradingResult

# Import logging from existing system
import sys
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import DataCollectionError


class LiveTradingType(Enum):
    """Enumeration of available live trading types."""
    REAL_TIME = "real_time"
    PAPER_TRADING = "paper_trading"
    SIMULATION = "simulation"
    BACKTESTING_LIVE = "backtesting_live"


class LiveTradingInterface(ABC):
    """Interface for all live trading implementations."""
    
    @abstractmethod
    def start_trading(self) -> LiveTradingResult:
        """Start the live trading system."""
        pass
    
    @abstractmethod
    def stop_trading(self) -> LiveTradingResult:
        """Stop the live trading system."""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        pass


class LiveTradingFactory(LoggerMixin):
    """
    Factory for creating live trading components.
    
    Follows the established factory pattern used throughout the system
    for consistent component creation and configuration.
    """
    
    def __init__(self, config: LiveTradingConfig):
        """
        Initialize live trading factory.
        
        Args:
            config: Live trading configuration
        """
        self.config = config
        self._traders: Dict[LiveTradingType, BaseLiveTrader] = {}
        
        # Registry of available live traders
        self._trader_registry: Dict[LiveTradingType, type] = {}
        self._register_traders()
    
    def _register_traders(self):
        """Register available live trader types."""
        # Import trader implementations
        try:
            from live_trading.real_time_trader import RealTimeLiveTrader
            self._trader_registry[LiveTradingType.REAL_TIME] = RealTimeLiveTrader
        except ImportError as e:
            self.logger.warning(f"RealTimeLiveTrader not available: {e}")

        try:
            from live_trading.paper_trader import PaperLiveTrader
            self._trader_registry[LiveTradingType.PAPER_TRADING] = PaperLiveTrader
        except ImportError as e:
            self.logger.warning(f"PaperLiveTrader not available: {e}")

        try:
            from live_trading.simulation_trader import SimulationLiveTrader
            self._trader_registry[LiveTradingType.SIMULATION] = SimulationLiveTrader
        except ImportError as e:
            self.logger.warning(f"SimulationLiveTrader not available: {e}")
        
        self.logger.info(f"Registered {len(self._trader_registry)} live trader types")
    
    def create_trader(self, trading_type: Union[LiveTradingType, str], **kwargs) -> BaseLiveTrader:
        """
        Create a live trader for the specified type.
        
        Args:
            trading_type: Type of live trader to create
            **kwargs: Additional parameters for trader creation
            
        Returns:
            Live trader instance
            
        Raises:
            DataCollectionError: If trader creation fails
        """
        if isinstance(trading_type, str):
            try:
                trading_type = LiveTradingType(trading_type.lower())
            except ValueError:
                raise DataCollectionError(f"Unknown live trading type: {trading_type}")
        
        # Return cached trader if available
        if trading_type in self._traders:
            return self._traders[trading_type]
        
        try:
            trader = self._create_trader_instance(trading_type, **kwargs)
            self._traders[trading_type] = trader
            self.logger.info(f"Created {trading_type.value} live trader")
            return trader
            
        except Exception as e:
            self.logger.error(f"Failed to create {trading_type.value} trader: {str(e)}")
            raise DataCollectionError(f"Trader creation failed: {str(e)}")
    
    def _create_trader_instance(self, trading_type: LiveTradingType, **kwargs) -> BaseLiveTrader:
        """Create the actual trader instance."""
        if trading_type not in self._trader_registry:
            raise DataCollectionError(f"Trader type {trading_type.value} not registered")
        
        trader_class = self._trader_registry[trading_type]
        
        # Merge config with kwargs
        trader_config = {
            **self.config.__dict__,
            **kwargs
        }
        
        return trader_class(trader_config, trading_type.value)
    
    def get_available_traders(self) -> List[LiveTradingType]:
        """Get list of available live trader types."""
        return list(self._trader_registry.keys())
    
    def validate_configuration(self) -> List[str]:
        """Validate factory configuration."""
        issues = []
        
        # Validate base configuration
        config_issues = self.config.validate()
        issues.extend(config_issues)
        
        # Check if any traders are available
        if not self._trader_registry:
            issues.append("No live trader implementations available")
        
        # Validate model path
        if not Path(self.config.model_path).exists():
            issues.append(f"Model file not found: {self.config.model_path}")
        
        return issues


class LiveTradingOrchestrator(LoggerMixin):
    """
    Orchestrates live trading operations across multiple components.
    
    Coordinates data collection, feature engineering, model inference,
    and trade execution in a real-time loop.
    """
    
    def __init__(self, config: LiveTradingConfig):
        """
        Initialize live trading orchestrator.
        
        Args:
            config: Live trading configuration
        """
        self.config = config
        self.factory = LiveTradingFactory(config)
        
        # Component instances
        self.trader = None
        self.data_collector = None
        self.feature_engine = None
        self.model_engine = None
        self.risk_manager = None
        
        # State management
        self.is_running = False
        self.start_time = None
        self.last_prediction_time = None
        self.last_execution_time = None
        
        # Performance tracking
        self.orchestrator_stats = {
            'total_cycles': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'trades_executed': 0,
            'avg_cycle_time_ms': 0.0,
            'avg_prediction_time_ms': 0.0,
            'avg_execution_time_ms': 0.0,
            'errors': [],
            'warnings': []
        }
    
    def initialize_components(self, trading_type: LiveTradingType = LiveTradingType.REAL_TIME) -> LiveTradingResult:
        """
        Initialize all trading components.
        
        Args:
            trading_type: Type of live trading to use
            
        Returns:
            LiveTradingResult with initialization status
        """
        try:
            self.logger.info("Initializing live trading components...")
            
            # Create main trader
            self.trader = self.factory.create_trader(trading_type)
            
            # Initialize data collection (integrate with existing system)
            from data_collection.factories import DataCollectorFactory
            from data_collection.config import Config
            
            base_config = Config()
            data_factory = DataCollectorFactory(base_config)
            self.data_collector = data_factory.create_collector('mt5')
            
            # Initialize feature engineering (integrate with existing system)
            from features.core.factories import FeatureEngineFactory
            from features.config.feature_config import FeatureConfig
            
            feature_config = FeatureConfig(base_config)
            feature_factory = FeatureEngineFactory(base_config, feature_config)
            self.feature_engine = feature_factory
            
            # Initialize model engine
            from .model_engine import LiveModelEngine
            self.model_engine = LiveModelEngine(self.config)
            
            # Initialize risk manager
            from .risk_manager import LiveRiskManager
            self.risk_manager = LiveRiskManager(self.config)
            
            self.logger.info("✓ All components initialized successfully")
            
            return LiveTradingResult(
                success=True,
                message="Components initialized successfully",
                data={
                    'trader_type': trading_type.value,
                    'components': ['trader', 'data_collector', 'feature_engine', 'model_engine', 'risk_manager']
                }
            )
            
        except Exception as e:
            error_msg = f"Component initialization failed: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def start_live_trading(self, trading_type: LiveTradingType = LiveTradingType.REAL_TIME) -> LiveTradingResult:
        """
        Start the complete live trading system.
        
        Args:
            trading_type: Type of live trading to use
            
        Returns:
            LiveTradingResult with startup status
        """
        try:
            # Initialize components if not already done
            if self.trader is None:
                init_result = self.initialize_components(trading_type)
                if not init_result.success:
                    return init_result
            
            # Start the trader
            start_result = self.trader.start_trading()
            if not start_result.success:
                return start_result
            
            self.is_running = True
            self.start_time = datetime.now()
            
            self.logger.info("🚀 Live trading system started successfully")
            
            return LiveTradingResult(
                success=True,
                message="Live trading system started",
                data={
                    'start_time': self.start_time.isoformat(),
                    'trading_type': trading_type.value,
                    'config': self.config.__dict__
                }
            )
            
        except Exception as e:
            error_msg = f"Failed to start live trading: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def stop_live_trading(self) -> LiveTradingResult:
        """Stop the live trading system."""
        try:
            if self.trader:
                stop_result = self.trader.stop_trading()
                if not stop_result.success:
                    self.logger.warning(f"Trader stop returned: {stop_result.message}")
            
            self.is_running = False
            
            self.logger.info("🛑 Live trading system stopped")
            
            return LiveTradingResult(
                success=True,
                message="Live trading system stopped",
                data=self.get_performance_summary()
            )
            
        except Exception as e:
            error_msg = f"Failed to stop live trading: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def get_status(self) -> Dict[str, Any]:
        """Get current system status."""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'trader_status': self.trader.get_performance_summary() if self.trader else None,
            'orchestrator_stats': self.orchestrator_stats,
            'components_initialized': {
                'trader': self.trader is not None,
                'data_collector': self.data_collector is not None,
                'feature_engine': self.feature_engine is not None,
                'model_engine': self.model_engine is not None,
                'risk_manager': self.risk_manager is not None
            }
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        summary = {
            'orchestrator_stats': self.orchestrator_stats,
            'system_status': self.get_status()
        }
        
        if self.trader:
            summary['trader_performance'] = self.trader.get_performance_summary()
        
        return summary
