"""
Real-Time Live Trader Implementation

Main implementation of live trading system with real-time data processing,
model inference, and trade execution coordination.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import time
import threading
from collections import deque

from .base import BaseLiveTrader, LiveTradingResult, LiveTrade, TradeStatus
from .real_time_data import RealTimeDataCollector
from .model_engine import LiveModelEngine
from .trade_execution import TradeExecutionEngine
from .risk_manager import LiveRiskManager

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from features.core.factories import FeatureEngineFactory, FeatureType
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class RealTimeLiveTrader(BaseLiveTrader):
    """
    Real-time live trader implementation.
    
    Coordinates all components in a real-time trading loop:
    Data Collection → Feature Engineering → Model Inference → Risk Management → Trade Execution
    """
    
    def __init__(self, config: Dict[str, Any], trader_type: str):
        """
        Initialize real-time live trader.
        
        Args:
            config: Trading configuration
            trader_type: Type of trader
        """
        super().__init__(config, trader_type)
        
        # Trading loop parameters
        self.prediction_interval = config.get('prediction_interval_seconds', 30)
        self.execution_interval = config.get('execution_interval_seconds', 60)
        
        # Component instances
        self.data_collector = None
        self.feature_engine = None
        self.model_engine = None
        self.execution_engine = None
        self.risk_manager = None
        
        # Threading control
        self.trading_thread = None
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # Performance tracking
        self.loop_stats = {
            'total_cycles': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'trades_attempted': 0,
            'trades_executed': 0,
            'avg_cycle_time_ms': 0.0,
            'last_cycle_time': None,
            'errors': [],
            'warnings': []
        }
        
        # Feature buffer for real-time processing
        self.feature_buffer = deque(maxlen=100)
        self.last_feature_time = None
    
    def start_trading(self) -> LiveTradingResult:
        """Start the real-time trading system."""
        try:
            if self.is_active:
                return LiveTradingResult(
                    success=False,
                    message="Trading system is already active"
                )
            
            self.logger.info("🚀 Starting real-time live trading system...")
            
            # Initialize all components
            init_result = self._initialize_components()
            if not init_result.success:
                return init_result
            
            # Start trading loop
            self.is_active = True
            self.start_time = datetime.now()
            self.stop_event.clear()
            
            # Start main trading thread
            self.trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
            self.trading_thread.start()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.logger.info("✅ Real-time trading system started successfully")
            
            return LiveTradingResult(
                success=True,
                message="Real-time trading started",
                data={
                    'start_time': self.start_time.isoformat(),
                    'prediction_interval': self.prediction_interval,
                    'execution_interval': self.execution_interval
                }
            )
            
        except Exception as e:
            error_msg = f"Failed to start real-time trading: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def stop_trading(self) -> LiveTradingResult:
        """Stop the real-time trading system."""
        try:
            if not self.is_active:
                return LiveTradingResult(
                    success=True,
                    message="Trading system is not active"
                )
            
            self.logger.info("🛑 Stopping real-time trading system...")
            
            # Signal threads to stop
            self.stop_event.set()
            self.is_active = False
            
            # Wait for threads to finish
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=10.0)
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5.0)
            
            # Disconnect components
            if self.data_collector:
                self.data_collector.disconnect()
            
            if self.execution_engine:
                self.execution_engine.disconnect()
            
            self.logger.info("✅ Real-time trading system stopped")
            
            return LiveTradingResult(
                success=True,
                message="Real-time trading stopped",
                data=self.get_performance_summary()
            )
            
        except Exception as e:
            error_msg = f"Error stopping trading system: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def _initialize_components(self) -> LiveTradingResult:
        """Initialize all trading components."""
        try:
            self.logger.info("Initializing trading components...")
            
            # Initialize data collector
            self.data_collector = RealTimeDataCollector(self.config)
            if not self.data_collector.start_streaming():
                raise Exception("Failed to start data streaming")
            
            # Initialize feature engine
            base_config = Config()
            feature_config = FeatureConfig(base_config)
            feature_factory = FeatureEngineFactory(base_config, feature_config)
            
            # Create feature engines - TRAINING-COMPATIBLE FEATURE SET for 240 features
            # Based on actual training data: xauusd_features_20250920_015446.csv
            self.feature_engines = {}
            feature_types = [
                FeatureType.PRICE_FEATURES,           # 128 features (price, returns, volatility)
                FeatureType.TECHNICAL_INDICATORS,     # 34 features (RSI, MACD, etc.)
                FeatureType.VOLATILITY_FEATURES,      # 48 features (ATR, Bollinger, etc.)
                FeatureType.VOLUME_FEATURES,          # 29 features (VWAP, OBV, volume analysis)
                # Note: Limiting to training-compatible features only
                # Cross-asset correlations: only 4 features (price-volume correlations)
                # Regime features: only 6 features (basic regime detection)
                # No session features (0 in training)
                # Minimal temporal features (1 in training)
            ]
            
            for feature_type in feature_types:
                try:
                    engine = feature_factory.create_engine(feature_type)
                    self.feature_engines[feature_type] = engine
                except Exception as e:
                    self.logger.warning(f"Failed to create {feature_type} engine: {str(e)}")
            
            # Initialize model engine
            self.model_engine = LiveModelEngine(self.config)
            if not self.model_engine.load_models():
                raise Exception("Failed to load models")
            
            # Initialize execution engine
            self.execution_engine = TradeExecutionEngine(self.config)
            if not self.execution_engine.connect():
                raise Exception("Failed to connect execution engine")
            
            # Initialize risk manager
            self.risk_manager = LiveRiskManager(self.config)
            
            self.logger.info("✅ All components initialized successfully")
            
            return LiveTradingResult(
                success=True,
                message="Components initialized"
            )
            
        except Exception as e:
            error_msg = f"Component initialization failed: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def _trading_loop(self):
        """Main trading loop."""
        self.logger.info("Trading loop started")
        
        while not self.stop_event.is_set():
            try:
                cycle_start_time = time.time()
                
                # Get latest market data
                latest_data = self.data_collector.get_latest_data(100)
                
                if latest_data.empty:
                    self.logger.warning("No market data available")
                    time.sleep(5)
                    continue
                
                # Generate features
                features = self._generate_features(latest_data)
                
                if features is None or features.empty:
                    self.logger.warning("Feature generation failed")
                    time.sleep(self.prediction_interval)
                    continue
                
                # Make model prediction
                prediction_result = self.model_engine.predict(features)
                
                if not prediction_result['success']:
                    self.logger.error(f"Model prediction failed: {prediction_result.get('error', 'Unknown error')}")
                    self.loop_stats['failed_predictions'] += 1
                    time.sleep(self.prediction_interval)
                    continue
                
                self.loop_stats['successful_predictions'] += 1
                
                # Check if we should execute a trade
                trading_signal = prediction_result['trading_signal']
                
                if trading_signal.get('should_enter', False):
                    self._attempt_trade_execution(trading_signal, latest_data)
                
                # Update positions
                self._update_existing_positions(latest_data)
                
                # Update loop statistics
                cycle_time_ms = (time.time() - cycle_start_time) * 1000
                self._update_loop_stats(cycle_time_ms)
                
                # Sleep until next cycle
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                self.loop_stats['errors'].append({
                    'timestamp': datetime.now(),
                    'error': str(e)
                })
                time.sleep(5)  # Wait before retrying
        
        self.logger.info("Trading loop stopped")
    
    def _generate_features(self, market_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Generate features from market data."""
        try:
            if len(market_data) < 50:  # Need sufficient history
                return None
            
            # Use the latest row for feature generation
            latest_row = market_data.iloc[-1:]
            
            # Generate features using existing engines
            all_features = []
            
            for feature_type, engine in self.feature_engines.items():
                try:
                    result = engine.generate_features(market_data)
                    if result.features is not None and not result.features.empty:
                        # Get the latest features
                        latest_features = result.features.iloc[-1:]
                        all_features.append(latest_features)
                except Exception as e:
                    self.logger.warning(f"Feature generation failed for {feature_type}: {str(e)}")
            
            if not all_features:
                return None
            
            # Combine all features
            combined_features = pd.concat(all_features, axis=1)
            
            # Handle any remaining NaN values
            combined_features = combined_features.fillna(method='ffill').fillna(0)
            
            return combined_features
            
        except Exception as e:
            self.logger.error(f"Feature generation error: {str(e)}")
            return None
    
    def _attempt_trade_execution(self, trading_signal: Dict[str, Any], market_data: pd.DataFrame):
        """Attempt to execute a trade based on signal."""
        try:
            self.loop_stats['trades_attempted'] += 1
            
            # Get current price
            current_price_info = self.data_collector.get_current_price()
            if not current_price_info:
                self.logger.warning("Could not get current price for trade execution")
                return
            
            current_price = current_price_info['ask'] if trading_signal['direction'] == 'long' else current_price_info['bid']
            
            # Risk validation
            risk_validation = self.risk_manager.validate_trade_risk(
                trading_signal, 
                self.risk_manager.current_balance,
                list(self.active_trades.values())
            )
            
            if not risk_validation['approved']:
                self.logger.info(f"Trade rejected by risk manager: {risk_validation['rejections']}")
                return
            
            # Execute trade
            trade = self.execution_engine.execute_model_driven_trade(
                trading_signal,
                current_price,
                self.risk_manager.current_balance
            )
            
            if trade:
                self.add_trade(trade)
                self.loop_stats['trades_executed'] += 1
                self.logger.info(f"✅ Trade executed: {trade.trade_id}")
            else:
                self.logger.warning("Trade execution failed")
                
        except Exception as e:
            self.logger.error(f"Trade execution attempt failed: {str(e)}")
    
    def _update_existing_positions(self, market_data: pd.DataFrame):
        """Update existing positions and check exit conditions."""
        if not self.active_trades:
            return
        
        try:
            current_price_info = self.data_collector.get_current_price()
            if not current_price_info:
                return
            
            current_price = (current_price_info['bid'] + current_price_info['ask']) / 2
            
            for trade_id, trade in list(self.active_trades.items()):
                # Update unrealized P&L
                trade.update_unrealized_pnl(current_price)
                
                # Check exit conditions (simplified - could be enhanced with model-driven exits)
                should_exit, exit_reason = self._check_exit_conditions(trade, current_price)
                
                if should_exit:
                    self.close_trade(trade_id, current_price, exit_reason)
                    self.risk_manager.update_trade_result(trade)
                    
        except Exception as e:
            self.logger.error(f"Position update error: {str(e)}")
    
    def _check_exit_conditions(self, trade: LiveTrade, current_price: float) -> tuple:
        """Check if trade should be exited."""
        # Simple exit logic - could be enhanced with model-driven exits
        
        # Check stop loss
        if trade.direction == trade.direction.LONG:
            if current_price <= trade.sl_price:
                return True, "stop_loss"
            if current_price >= trade.tp1_price:
                return True, "take_profit_1"
        else:
            if current_price >= trade.sl_price:
                return True, "stop_loss"
            if current_price <= trade.tp1_price:
                return True, "take_profit_1"
        
        # Check time-based exit (simplified)
        time_in_trade = datetime.now() - trade.entry_time
        if time_in_trade > timedelta(hours=24):  # Max 24 hours
            return True, "time_exit"
        
        return False, None
    
    def _monitoring_loop(self):
        """Monitoring loop for performance tracking."""
        while not self.stop_event.is_set():
            try:
                # Log performance summary every 5 minutes
                if self.loop_stats['total_cycles'] % 10 == 0 and self.loop_stats['total_cycles'] > 0:
                    self._log_performance_summary()
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {str(e)}")
                time.sleep(60)
    
    def _update_loop_stats(self, cycle_time_ms: float):
        """Update trading loop statistics."""
        self.loop_stats['total_cycles'] += 1
        
        # Update average cycle time
        total_cycles = self.loop_stats['total_cycles']
        self.loop_stats['avg_cycle_time_ms'] = (
            (self.loop_stats['avg_cycle_time_ms'] * (total_cycles - 1) + cycle_time_ms) / total_cycles
        )
        
        self.loop_stats['last_cycle_time'] = datetime.now()
    
    def _log_performance_summary(self):
        """Log performance summary."""
        summary = self.get_performance_summary()
        
        self.logger.info(f"📊 Performance Summary - "
                        f"Cycles: {self.loop_stats['total_cycles']}, "
                        f"Predictions: {self.loop_stats['successful_predictions']}, "
                        f"Trades: {self.loop_stats['trades_executed']}, "
                        f"Active: {len(self.active_trades)}, "
                        f"P&L: ${summary.get('total_pnl', 0):.2f}")
    
    def execute_trade(self, signal: Dict[str, Any]) -> LiveTradingResult:
        """Execute a trade (interface method)."""
        # This method is called by the base class interface
        # The actual execution happens in the trading loop
        return LiveTradingResult(
            success=True,
            message="Trade signal queued for execution"
        )
    
    def update_positions(self) -> LiveTradingResult:
        """Update positions (interface method)."""
        # Position updates happen in the trading loop
        return LiveTradingResult(
            success=True,
            message="Positions updated in trading loop",
            data={
                'active_trades': len(self.active_trades),
                'last_update': datetime.now().isoformat()
            }
        )
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get current account information."""
        return {
            'balance': self.risk_manager.current_balance if self.risk_manager else 0.0,
            'equity': self.risk_manager.current_balance + sum(t.unrealized_pnl for t in self.active_trades.values()),
            'margin_used': len(self.active_trades) * 1000,  # Simplified
            'free_margin': 10000,  # Simplified
            'active_trades': len(self.active_trades),
            'daily_pnl': self.risk_manager.daily_pnl if self.risk_manager else 0.0
        }
