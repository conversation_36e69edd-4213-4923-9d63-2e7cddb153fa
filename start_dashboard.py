#!/usr/bin/env python3
"""
Dashboard Launcher Script

Entry point for starting the XAUUSD AI Trading Dashboard.
Integrates with the existing trading system and provides a web interface
for monitoring and analysis.

Usage:
    python start_dashboard.py [options]

Examples:
    python start_dashboard.py                    # Start with default settings
    python start_dashboard.py --port 8082       # Start on custom port
    python start_dashboard.py --debug           # Start in debug mode
    python start_dashboard.py --config custom.yaml  # Use custom config

Author: AI Trading System
Version: 1.0.0
"""

import argparse
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

try:
    from dashboard.app import DashboardApp, create_app
    from data_collection.error_handling.logger import get_logger
except ImportError as e:
    print(f"Error importing dashboard modules: {e}")
    print("Please ensure all dependencies are installed:")
    print("pip install fastapi uvicorn jinja2 python-multipart")
    sys.exit(1)


def check_dependencies():
    """Check if required dependencies are available."""
    required_packages = [
        'fastapi',
        'uvicorn',
        'jinja2',
        'websockets'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_system_requirements():
    """Check system requirements and existing components."""
    logger = get_logger("DashboardLauncher")
    
    # Check if virtual environment is active
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("Virtual environment not detected. Consider using venv for isolation.")
    
    # Check if data directories exist
    data_dirs = [
        'data',
        'backtest/results',
        'forward_test/results',
        'logs'
    ]
    
    for data_dir in data_dirs:
        dir_path = project_root / data_dir
        if not dir_path.exists():
            logger.warning(f"Data directory not found: {data_dir}")
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {data_dir}")
    
    # Check if config exists
    config_path = project_root / "config" / "config.yaml"
    if not config_path.exists():
        logger.warning("Main config file not found. Dashboard will use defaults.")
    
    return True


def main():
    """Main entry point for dashboard launcher."""
    parser = argparse.ArgumentParser(
        description="XAUUSD AI Trading Dashboard",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Start with default settings
  %(prog)s --port 8082               # Start on custom port  
  %(prog)s --debug                   # Start in debug mode
  %(prog)s --config custom.yaml      # Use custom config
  %(prog)s --host 0.0.0.0 --port 80  # Bind to all interfaces on port 80

Dashboard Features:
  - Real-time XAUUSD price charts and indicators
  - Live trading positions and P&L monitoring
  - System status and model performance metrics
  - Historical backtest and forward test results
  - Professional dark theme optimized for trading

The dashboard integrates with your existing trading system and provides
comprehensive visibility into all aspects of the AI trading operation.
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0 for network access)"
    )
    
    parser.add_argument(
        "--port", "-p",
        type=int,
        default=8081,
        help="Port to bind to (default: 8081)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode with auto-reload"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes (default: 1)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=['debug', 'info', 'warning', 'error'],
        default='info',
        help="Logging level (default: info)"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check dependencies and exit"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Dashboard v1.0.0"
    )
    
    args = parser.parse_args()
    
    # Check dependencies if requested
    if args.check_deps:
        if check_dependencies():
            print("✓ All dependencies are available")
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check system requirements
    if not check_system_requirements():
        print("System requirements check failed")
        sys.exit(1)
    
    try:
        print("🚀 Starting XAUUSD AI Trading Dashboard...")
        print(f"   Host: {args.host}")
        print(f"   Port: {args.port}")
        print(f"   Debug: {args.debug}")
        print(f"   Config: {args.config or 'default'}")
        print()
        
        # Create dashboard application
        app = create_app(args.config)
        
        # Override configuration with command line arguments
        if args.host:
            app.dashboard_config.host = args.host
        if args.port:
            app.dashboard_config.port = args.port
        if args.debug:
            app.dashboard_config.debug = True
            app.dashboard_config.auto_reload = True
        
        # Set logging level
        if args.log_level:
            app.dashboard_config.logging_config['level'] = args.log_level.upper()
        
        print(f"Dashboard will be available at: http://{args.host}:{args.port}")
        print("Press Ctrl+C to stop the dashboard")
        print()
        
        # Run the application
        app.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
