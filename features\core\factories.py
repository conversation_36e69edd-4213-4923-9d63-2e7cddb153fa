"""
Feature Engineering Factory Pattern Implementation

Provides factory classes for creating feature engineering components following
clean architecture principles and enabling easy extensibility.
"""

from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
from enum import Enum
import pandas as pd
from datetime import datetime

# Import base classes
from .base_feature import BaseFeatureEngine, FeatureEngineInterface, FeatureResult
from ..config.feature_config import FeatureConfig

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from data_collection.config import Config
from data_collection.error_handling.logger import LoggerMixin, get_logger, log_execution_time


class FeatureType(Enum):
    """Enumeration of available feature types."""
    PRICE_FEATURES = "price_features"
    TECHNICAL_INDICATORS = "technical_indicators"
    VOLATILITY_FEATURES = "volatility_features"
    VOLUME_FEATURES = "volume_features"
    CORRELATION_FEATURES = "correlation_features"
    REGIME_FEATURES = "regime_features"
    CONTEXT_FEATURES = "context_features"
    TEMPORAL_FEATURES = "temporal_features"
    FEATURE_SCALING = "feature_scaling"
    FEATURE_SELECTION = "feature_selection"
    FEATURE_PROCESSING = "feature_processing"


class FeatureEngineFactory:
    """
    Factory for creating feature engineering components.
    
    Follows the established factory pattern used throughout the system
    for consistent component creation and configuration.
    """
    
    def __init__(self, config: Config, feature_config: Optional[FeatureConfig] = None):
        """
        Initialize feature engine factory.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
        """
        self.config = config
        self.feature_config = feature_config or FeatureConfig(config)
        self.logger = get_logger(self.__class__.__name__)
        
        # Registry of available feature engines
        self._engine_registry = {}
        self._register_engines()
    
    def _register_engines(self) -> None:
        """Register available feature engines."""
        # Import feature engines (will be implemented in subsequent phases)
        try:
            from .price_features import MultiTimeframePriceFeatures
            self._engine_registry[FeatureType.PRICE_FEATURES] = MultiTimeframePriceFeatures
        except ImportError:
            self.logger.debug("Price features engine not yet available")
        
        try:
            from ..technical.momentum import MomentumIndicators
            from ..technical.volatility import VolatilityIndicators
            from ..technical.volume import VolumeIndicators
            self._engine_registry[FeatureType.TECHNICAL_INDICATORS] = MomentumIndicators
            self._engine_registry[FeatureType.VOLATILITY_FEATURES] = VolatilityIndicators
            self._engine_registry[FeatureType.VOLUME_FEATURES] = VolumeIndicators
        except ImportError as e:
            self.logger.debug(f"Technical indicators engine not yet available: {str(e)}")
        
        try:
            from ..correlations.cross_asset import CrossAssetCorrelations
            from ..correlations.regime_detection import RegimeDetection
            self._engine_registry[FeatureType.CORRELATION_FEATURES] = CrossAssetCorrelations
            self._engine_registry[FeatureType.REGIME_FEATURES] = RegimeDetection
        except ImportError as e:
            self.logger.debug(f"Correlation features engine not yet available: {str(e)}")
        
        try:
            from ..context.sessions import TradingSessionFeatures
            from ..context.temporal import TemporalPatternFeatures
            self._engine_registry[FeatureType.CONTEXT_FEATURES] = TradingSessionFeatures
            self._engine_registry[FeatureType.TEMPORAL_FEATURES] = TemporalPatternFeatures
        except ImportError as e:
            self.logger.debug(f"Context features engine not yet available: {str(e)}")

        try:
            from ..processing.scaling import FeatureScalingEngine
            from ..processing.selection import FeatureSelectionEngine
            from ..processing.pipeline import FeatureProcessingEngine
            self._engine_registry[FeatureType.FEATURE_SCALING] = FeatureScalingEngine
            self._engine_registry[FeatureType.FEATURE_SELECTION] = FeatureSelectionEngine
            self._engine_registry[FeatureType.FEATURE_PROCESSING] = FeatureProcessingEngine
        except ImportError as e:
            self.logger.debug(f"Processing engines not yet available: {str(e)}")
    
    def create_engine(self, feature_type: FeatureType, **kwargs) -> BaseFeatureEngine:
        """
        Create a feature engine of the specified type.
        
        Args:
            feature_type: Type of feature engine to create
            **kwargs: Additional parameters for engine creation
            
        Returns:
            Configured feature engine instance
            
        Raises:
            ValueError: If feature type is not supported
        """
        if feature_type not in self._engine_registry:
            available_types = list(self._engine_registry.keys())
            raise ValueError(f"Unsupported feature type: {feature_type}. Available: {available_types}")
        
        engine_class = self._engine_registry[feature_type]
        
        try:
            # Create engine with configuration
            engine = engine_class(self.config, self.feature_config, **kwargs)
            self.logger.info(f"Created {feature_type.value} engine: {engine_class.__name__}")
            return engine
            
        except Exception as e:
            self.logger.error(f"Failed to create {feature_type.value} engine: {str(e)}")
            raise
    
    def get_available_engines(self) -> List[FeatureType]:
        """Get list of available feature engine types."""
        return list(self._engine_registry.keys())
    
    def create_all_engines(self) -> Dict[FeatureType, BaseFeatureEngine]:
        """
        Create all available feature engines.
        
        Returns:
            Dictionary mapping feature types to engine instances
        """
        engines = {}
        
        for feature_type in self._engine_registry.keys():
            try:
                engines[feature_type] = self.create_engine(feature_type)
            except Exception as e:
                self.logger.warning(f"Failed to create {feature_type.value} engine: {str(e)}")
        
        return engines


class FeatureEngineOrchestrator(LoggerMixin):
    """
    Orchestrates the complete feature engineering pipeline.
    
    Manages the execution of multiple feature engines and combines
    their results into a comprehensive feature dataset.
    """
    
    def __init__(self, config: Config, feature_config: Optional[FeatureConfig] = None):
        """
        Initialize feature engineering orchestrator.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
        """
        self.config = config
        self.feature_config = feature_config or FeatureConfig(config)
        self.factory = FeatureEngineFactory(config, self.feature_config)
        
        # Initialize engines
        self.engines = {}
        self.results = {}
        
        # Processing statistics
        self.processing_stats = {
            'start_time': None,
            'end_time': None,
            'total_features_generated': 0,
            'engines_executed': 0,
            'errors': [],
            'warnings': []
        }
    
    @log_execution_time
    def generate_all_features(self, data: pd.DataFrame, **kwargs) -> Dict[str, FeatureResult]:
        """
        Generate features using all available engines.
        
        Args:
            data: Input price data
            **kwargs: Additional parameters for feature generation
            
        Returns:
            Dictionary mapping feature types to results
        """
        self.logger.info("Starting comprehensive feature generation")
        self.processing_stats['start_time'] = datetime.now()
        
        # Reset results
        self.results = {}
        
        # Create all available engines
        self.engines = self.factory.create_all_engines()
        
        # Generate features with each engine
        for feature_type, engine in self.engines.items():
            try:
                self.logger.info(f"Generating {feature_type.value} features")
                result = engine.generate_features(data, **kwargs)
                
                if result.features is not None and not result.features.empty:
                    self.results[feature_type.value] = result
                    self.processing_stats['total_features_generated'] += len(result.feature_names)
                    self.processing_stats['engines_executed'] += 1
                    
                    self.logger.info(f"Generated {len(result.feature_names)} {feature_type.value} features")
                else:
                    self.logger.warning(f"No features generated for {feature_type.value}")
                    
            except Exception as e:
                error_msg = f"Error generating {feature_type.value} features: {str(e)}"
                self.logger.error(error_msg)
                self.processing_stats['errors'].append(error_msg)
        
        self.processing_stats['end_time'] = datetime.now()
        
        # Log summary
        self._log_generation_summary()
        
        return self.results
    
    def combine_features(self, results: Optional[Dict[str, FeatureResult]] = None) -> pd.DataFrame:
        """
        Combine features from all engines into a single DataFrame.
        
        Args:
            results: Optional results dictionary, uses self.results if None
            
        Returns:
            Combined features DataFrame
        """
        results = results or self.results
        
        if not results:
            self.logger.warning("No feature results to combine")
            return pd.DataFrame()
        
        combined_features = None
        feature_metadata = {}
        
        for feature_type, result in results.items():
            if result.features is not None and not result.features.empty:
                if combined_features is None:
                    combined_features = result.features.copy()
                else:
                    # Align indices and combine
                    combined_features = pd.concat([combined_features, result.features], axis=1, join='inner')
                
                # Combine metadata
                feature_metadata.update(result.feature_metadata)
        
        if combined_features is not None:
            self.logger.info(f"Combined features: {len(combined_features.columns)} features, {len(combined_features)} records")
            return combined_features

        return pd.DataFrame()
    
    def save_features(self, features: pd.DataFrame, metadata: Optional[Dict[str, Any]] = None, 
                     filename: Optional[str] = None) -> str:
        """
        Save generated features to file.
        
        Args:
            features: Features DataFrame to save
            metadata: Optional metadata to save alongside features
            filename: Optional custom filename
            
        Returns:
            Path to saved file
        """
        if features is None or features.empty:
            raise ValueError("No features to save")
        
        # Create output directory
        output_path = Path(self.feature_config.output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"xauusd_features_{timestamp}.csv"
        
        file_path = output_path / filename
        
        # Save features
        features.to_csv(file_path, index=True)
        
        # Save metadata if provided
        if metadata and self.feature_config.output.get('include_metadata', True):
            metadata_path = file_path.with_suffix('.json')
            import json
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
        
        self.logger.info(f"Saved {len(features.columns)} features to {file_path}")
        return str(file_path)
    
    def _log_generation_summary(self) -> None:
        """Log summary of feature generation process."""
        stats = self.processing_stats
        duration = (stats['end_time'] - stats['start_time']).total_seconds()
        
        self.logger.info("=" * 60)
        self.logger.info("FEATURE GENERATION SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total features generated: {stats['total_features_generated']}")
        self.logger.info(f"Engines executed: {stats['engines_executed']}")
        self.logger.info(f"Processing time: {duration:.2f} seconds")
        
        if stats['errors']:
            self.logger.warning(f"Errors encountered: {len(stats['errors'])}")
            for error in stats['errors']:
                self.logger.warning(f"  - {error}")
        
        if stats['warnings']:
            self.logger.warning(f"Warnings: {len(stats['warnings'])}")
        
        self.logger.info("=" * 60)
    
    def get_feature_summary(self) -> Dict[str, Any]:
        """
        Get summary of generated features.
        
        Returns:
            Dictionary with feature generation summary
        """
        summary = {
            'total_feature_types': len(self.results),
            'feature_breakdown': {},
            'processing_stats': self.processing_stats.copy()
        }
        
        for feature_type, result in self.results.items():
            summary['feature_breakdown'][feature_type] = {
                'feature_count': len(result.feature_names),
                'feature_names': result.feature_names,
                'has_errors': len(result.errors) > 0,
                'has_warnings': len(result.warnings) > 0
            }
        
        return summary
