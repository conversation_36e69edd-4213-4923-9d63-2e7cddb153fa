{% extends "base.html" %}

{% block title %}System Health - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">System Health</h2>
                        <p class="text-muted mb-0">Real-time system monitoring and status</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success" id="system-status">System Online</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Overview -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>MT5 Connection</h6>
                    <i class="fas fa-plug text-success" id="mt5-icon"></i>
                </div>
                <div class="metric-value text-success" id="mt5-status">Connected</div>
                <div class="metric-change">
                    <small class="text-muted">Server: <span id="mt5-server">MetaQuotes-Demo</span></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Model Engine</h6>
                    <i class="fas fa-brain text-info" id="model-icon"></i>
                </div>
                <div class="metric-value text-info" id="model-status">Active</div>
                <div class="metric-change">
                    <small class="text-muted">Models: <span id="model-count">4</span> loaded</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>Data Feed</h6>
                    <i class="fas fa-stream text-warning" id="data-icon"></i>
                </div>
                <div class="metric-value text-warning" id="data-status">Streaming</div>
                <div class="metric-change">
                    <small class="text-muted">Latency: <span id="data-latency">12ms</span></small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="metric-card">
                <div class="metric-header">
                    <h6>System Uptime</h6>
                    <i class="fas fa-clock text-primary" id="uptime-icon"></i>
                </div>
                <div class="metric-value text-primary" id="system-uptime">2d 14h 32m</div>
                <div class="metric-change">
                    <small class="text-muted">Started: <span id="start-time">Dec 19, 09:15</span></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Resource Usage -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">CPU Usage</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Current Usage</span>
                        <span class="fw-bold" id="cpu-percentage">24.5%</span>
                    </div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar bg-info" id="cpu-progress" style="width: 24.5%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">1min</small><br>
                            <span class="fw-bold" id="cpu-1min">22.1%</span>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">5min</small><br>
                            <span class="fw-bold" id="cpu-5min">25.8%</span>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">15min</small><br>
                            <span class="fw-bold" id="cpu-15min">28.2%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Memory Usage</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>RAM Usage</span>
                        <span class="fw-bold" id="memory-percentage">68.2%</span>
                    </div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar bg-warning" id="memory-progress" style="width: 68.2%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">Used</small><br>
                            <span class="fw-bold" id="memory-used">5.46 GB</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Available</small><br>
                            <span class="fw-bold" id="memory-available">2.54 GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Disk Usage</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Storage Usage</span>
                        <span class="fw-bold" id="disk-percentage">45.8%</span>
                    </div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar bg-success" id="disk-progress" style="width: 45.8%"></div>
                    </div>
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">Used</small><br>
                            <span class="fw-bold" id="disk-used">458 GB</span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Free</small><br>
                            <span class="fw-bold" id="disk-free">542 GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Status -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Service Status</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><i class="fas fa-circle text-success me-2"></i>MT5 Terminal</td>
                                    <td><span class="badge bg-success">Running</span></td>
                                    <td class="text-end">99.9%</td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-circle text-success me-2"></i>Model Engine</td>
                                    <td><span class="badge bg-success">Active</span></td>
                                    <td class="text-end">99.8%</td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-circle text-success me-2"></i>Data Collector</td>
                                    <td><span class="badge bg-success">Streaming</span></td>
                                    <td class="text-end">99.9%</td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-circle text-success me-2"></i>WebSocket Server</td>
                                    <td><span class="badge bg-success">Connected</span></td>
                                    <td class="text-end">100%</td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-circle text-warning me-2"></i>Risk Manager</td>
                                    <td><span class="badge bg-warning">Monitoring</span></td>
                                    <td class="text-end">99.5%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="trading-card">
                <div class="card-header">
                    <h5 class="mb-0">Network Status</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="metric-value text-info" id="network-in">1.2 MB/s</div>
                                <small class="text-muted">Incoming</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="metric-value text-warning" id="network-out">0.8 MB/s</div>
                                <small class="text-muted">Outgoing</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <canvas id="networkChart" height="150"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Logs -->
    <div class="row">
        <div class="col-12">
            <div class="trading-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent System Logs</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="log-container" id="system-logs" style="height: 300px; overflow-y: auto; background: #1a1a1a; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 0.9rem;">
                        <div class="log-entry text-success">[2024-12-21 00:50:18] INFO: Dashboard application initialized successfully</div>
                        <div class="log-entry text-info">[2024-12-21 00:50:18] INFO: WebSocket handler initialized</div>
                        <div class="log-entry text-success">[2024-12-21 00:50:21] INFO: WebSocket connected. Total connections: 1</div>
                        <div class="log-entry text-warning">[2024-12-21 00:50:25] WARN: MT5 provider not available - real data integration needed</div>
                        <div class="log-entry text-info">[2024-12-21 00:50:30] INFO: Model engine predictions updated</div>
                        <div class="log-entry text-success">[2024-12-21 00:50:35] INFO: System health check completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize system page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize network chart
    initializeNetworkChart();
    
    // Start real-time updates
    startSystemMonitoring();
    
    // Set up WebSocket updates if available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        console.log('System page initialized with real-time monitoring');
    }
});

function initializeNetworkChart() {
    const networkCtx = document.getElementById('networkChart');
    if (networkCtx) {
        new Chart(networkCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Incoming',
                    data: [],
                    borderColor: '#00aaff',
                    backgroundColor: 'rgba(0, 170, 255, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: 'Outgoing',
                    data: [],
                    borderColor: '#ffaa00',
                    backgroundColor: 'rgba(255, 170, 0, 0.1)',
                    borderWidth: 2,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#ffffff' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    }
}

function startSystemMonitoring() {
    // Update system metrics every 5 seconds
    setInterval(updateSystemMetrics, 5000);
}

function updateSystemMetrics() {
    // This will be connected to real WebSocket data
    console.log('Updating system metrics...');
}

function refreshLogs() {
    // Refresh system logs
    console.log('Refreshing system logs...');
}

function clearLogs() {
    const logsContainer = document.getElementById('system-logs');
    if (logsContainer) {
        logsContainer.innerHTML = '<div class="log-entry text-info">[' + new Date().toISOString() + '] INFO: Logs cleared</div>';
    }
}
</script>
{% endblock %}
