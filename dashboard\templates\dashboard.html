{% extends "base.html" %}

{% block title %}{{ title }} - Dashboard{% endblock %}

{% block content %}
<!-- Professional Header with Connection Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-0 text-gold">
                    <i class="fas fa-chart-line me-2"></i>
                    XAUUSD AI Trading Dashboard
                </h1>
                <p class="text-muted">Real-time algorithmic trading system monitoring</p>
            </div>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <small class="text-muted d-block">Last Update</small>
                    <strong id="last-update-time">--:--:--</strong>
                </div>
                <span id="connection-status" class="connection-status">Connecting...</span>
            </div>
        </div>
    </div>
</div>

<!-- Professional Price Ticker -->
<div class="row mb-4">
    <div class="col-12">
        <div class="trading-card">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h2 class="text-gold mb-0 me-3">XAUUSD!</h2>
                    <div class="price-display" id="current-price">$2,650.25</div>
                    <div class="price-change positive" id="price-change">+2.45</div>
                    <small class="text-muted ms-3">(<span id="price-change-percent">+0.09%</span>)</small>
                </div>
                <div class="d-flex align-items-center">
                    <div class="me-4 text-center">
                        <small class="text-muted d-block">Volume</small>
                        <strong id="volume">1,234,567</strong>
                    </div>
                    <div class="me-4 text-center">
                        <small class="text-muted d-block">Spread</small>
                        <strong id="spread">0.35</strong>
                    </div>
                    <div class="text-center">
                        <small class="text-muted d-block">Session</small>
                        <strong id="trading-session">US</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Performance Metrics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">Daily P&L</span>
                <i class="fas fa-chart-line text-profit"></i>
            </div>
            <div class="metric-value text-profit" id="daily-pnl">+$125.50</div>
            <small class="text-muted">Today's Performance</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">Account Balance</span>
                <i class="fas fa-wallet text-gold"></i>
            </div>
            <div class="metric-value text-gold" id="account-balance">$10,250.00</div>
            <small class="text-muted">Available Equity</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">Win Rate</span>
                <i class="fas fa-target text-info"></i>
            </div>
            <div class="metric-value text-info" id="win-rate">75.0%</div>
            <small class="text-muted">Success Rate</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">Total Trades</span>
                <i class="fas fa-exchange-alt text-neutral"></i>
            </div>
            <div class="metric-value" id="total-trades">8</div>
            <small class="text-muted">Today</small>
        </div>
    </div>
</div>

<!-- System Health Metrics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">System Status</span>
                <span id="system-status-badge" class="badge bg-success">ONLINE</span>
            </div>
            <div class="metric-value text-success" id="system-status">Running</div>
            <small class="text-muted">Uptime: <span id="uptime">2h 15m</span></small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="metric-label">CPU Usage</div>
            <div class="metric-value" id="cpu-usage">25.4%</div>
            <div class="progress progress-sm mt-2">
                <div class="progress-bar bg-info" id="cpu-progress" style="width: 25%"></div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="metric-label">Memory Usage</div>
            <div class="metric-value" id="memory-usage">45.2%</div>
            <div class="progress progress-sm mt-2">
                <div class="progress-bar bg-warning" id="memory-progress" style="width: 45%"></div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="metric-card">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="metric-label">Active Models</span>
                <i class="fas fa-brain text-accent-primary"></i>
            </div>
            <div class="metric-value text-accent-primary" id="active-models">4</div>
            <small class="text-muted">LightGBM, CatBoost, XGBoost, Ensemble</small>
        </div>
    </div>
</div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="small">Total P&L</div>
                        <div class="h5 mb-0" id="total-pnl">$0.00</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="small">Win Rate</div>
                        <div class="h5 mb-0" id="win-rate">0%</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-candlestick me-2"></i>
                    XAUUSD Live Chart
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" data-timeframe="5m">5M</button>
                    <button type="button" class="btn btn-outline-primary" data-timeframe="15m">15M</button>
                    <button type="button" class="btn btn-outline-primary" data-timeframe="1h">1H</button>
                    <button type="button" class="btn btn-outline-primary" data-timeframe="4h">4H</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="price-chart" height="400"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Performance Metrics
                </h5>
            </div>
            <div class="card-body">
                <canvas id="performance-chart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Trading Activity Row -->
<div class="row mb-4">
    <div class="col-lg-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    Open Positions
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="positions-table">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Direction</th>
                                <th>Size</th>
                                <th>Entry</th>
                                <th>Current</th>
                                <th>P&L</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">No open positions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-signal me-2"></i>
                    Recent Signals
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="signals-table">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Signal</th>
                                <th>Confidence</th>
                                <th>Price</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center text-muted">No recent signals</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information Row -->
<div class="row">
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>
                    System Health
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h4 mb-0 text-success" id="mt5-status">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="small text-muted">MT5</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h4 mb-0 text-success" id="models-status">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="small text-muted">Models</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="h4 mb-0 text-success" id="data-status">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="small text-muted">Data Feed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-brain me-2"></i>
                    Model Consensus
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span class="small">LightGBM</span>
                        <span class="small" id="lightgbm-confidence">0%</span>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-primary" id="lightgbm-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span class="small">CatBoost</span>
                        <span class="small" id="catboost-confidence">0%</span>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-success" id="catboost-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span class="small">XGBoost</span>
                        <span class="small" id="xgboost-confidence">0%</span>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-info" id="xgboost-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="mb-0">
                    <div class="d-flex justify-content-between">
                        <span class="small">Ensemble</span>
                        <span class="small" id="ensemble-confidence">0%</span>
                    </div>
                    <div class="progress progress-sm">
                        <div class="progress-bar bg-warning" id="ensemble-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    System Uptime
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="h3 mb-0" id="uptime-display">00:00:00</div>
                <div class="small text-muted">Hours:Minutes:Seconds</div>
                <hr>
                <div class="small">
                    <div>Last Signal: <span id="last-signal-time">Never</span></div>
                    <div>Last Update: <span id="last-data-update">Never</span></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function initializePage() {
    // Initialize charts
    initializePriceChart();
    initializePerformanceChart();
    
    // Start data updates
    startDataUpdates();
}

function initializePriceChart() {
    const ctx = document.getElementById('price-chart').getContext('2d');
    window.priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'XAUUSD',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'minute'
                    }
                },
                y: {
                    beginAtZero: false
                }
            }
        }
    });
}

function initializePerformanceChart() {
    const ctx = document.getElementById('performance-chart').getContext('2d');
    window.performanceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Wins', 'Losses'],
            datasets: [{
                data: [0, 0],
                backgroundColor: ['#28a745', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function startDataUpdates() {
    // This will be connected to WebSocket updates
    setInterval(updateDashboardData, 5000);
}

function updateDashboardData() {
    // Fetch latest data via API or WebSocket
    // This is a placeholder - will be implemented with real data
}
</script>
{% endblock %}
