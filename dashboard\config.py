"""
Dashboard Configuration Module

Handles configuration management for the dashboard system
following the established configuration pattern.
"""

import os
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

# Import from existing system
import sys
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.config import Config as BaseConfig
from data_collection.error_handling.exceptions import ConfigurationError


@dataclass
class DashboardConfig:
    """
    Dashboard configuration container.
    
    Contains all configuration parameters for the dashboard system
    including web server settings, data sources, and UI preferences.
    """
    
    # Base configuration
    base_config: BaseConfig
    
    # Web Server Configuration
    host: str = "127.0.0.1"
    port: int = 8081
    debug: bool = False
    auto_reload: bool = True
    workers: int = 1
    
    # Dashboard Settings
    dashboard_title: str = "Apex v4 Trading Dashboard"
    theme: str = "dark"
    update_interval_seconds: int = 5
    max_chart_points: int = 1000
    timezone: str = "UTC"
    
    # Data Source Configuration
    data_sources: Dict[str, Any] = field(default_factory=lambda: {
        'mt5_realtime': {
            'enabled': True,
            'symbol': 'XAUUSD!',
            'timeframe': '5m',
            'max_bars': 1000
        },
        'backtest_results': {
            'enabled': True,
            'results_path': 'backtest/results',
            'max_results': 50
        },
        'forward_test_results': {
            'enabled': True,
            'results_path': 'forward_test/results',
            'max_results': 50
        },
        'model_outputs': {
            'enabled': True,
            'models_path': 'data/models',
            'cache_duration_minutes': 5
        },
        'account_data': {
            'enabled': True,
            'update_interval_seconds': 10
        }
    })
    
    # UI Configuration
    ui_config: Dict[str, Any] = field(default_factory=lambda: {
        'chart_height': 400,
        'chart_theme': 'dark',
        'show_grid': True,
        'show_volume': True,
        'show_indicators': True,
        'default_timeframe': '5m',
        'responsive_breakpoints': {
            'mobile': 768,
            'tablet': 1024,
            'desktop': 1200
        }
    })
    
    # Performance Configuration
    performance_config: Dict[str, Any] = field(default_factory=lambda: {
        'cache_enabled': True,
        'cache_ttl_seconds': 30,
        'max_concurrent_requests': 100,
        'request_timeout_seconds': 30,
        'websocket_ping_interval': 20,
        'websocket_ping_timeout': 10
    })
    
    # Security Configuration
    security_config: Dict[str, Any] = field(default_factory=lambda: {
        'cors_enabled': True,
        'cors_origins': ['*'],  # Allow all origins for network access
        'rate_limiting_enabled': True,
        'max_requests_per_minute': 1000,
        'session_timeout_minutes': 60
    })
    
    # Logging Configuration
    logging_config: Dict[str, Any] = field(default_factory=lambda: {
        'level': 'INFO',
        'file_enabled': True,
        'file_path': 'logs/dashboard.log',
        'max_file_size_mb': 10,
        'backup_count': 5,
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    })
    
    # Static Files Configuration
    static_config: Dict[str, Any] = field(default_factory=lambda: {
        'static_path': 'dashboard/static',
        'templates_path': 'dashboard/templates',
        'assets_path': 'dashboard/assets',
        'cache_static_files': True,
        'static_url_prefix': '/static'
    })
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        self._validate_config()
        self._setup_paths()
    
    def _validate_config(self):
        """Validate configuration parameters."""
        errors = []
        
        # Validate port
        if not (1024 <= self.port <= 65535):
            errors.append(f"Invalid port number: {self.port}. Must be between 1024-65535")
        
        # Validate update interval
        if self.update_interval_seconds < 1:
            errors.append("Update interval must be at least 1 second")
        
        # Validate data source paths
        for source, config in self.data_sources.items():
            if 'path' in config:
                path = Path(config['path'])
                if not path.exists():
                    errors.append(f"Data source path does not exist: {path}")
        
        # Validate UI configuration
        if self.ui_config['chart_height'] < 200:
            errors.append("Chart height must be at least 200 pixels")
        
        if errors:
            raise ConfigurationError(f"Dashboard configuration errors: {'; '.join(errors)}")
    
    def _setup_paths(self):
        """Setup and create necessary directories."""
        paths_to_create = [
            self.static_config['static_path'],
            self.static_config['templates_path'],
            self.static_config['assets_path'],
            Path(self.logging_config['file_path']).parent
        ]
        
        for path in paths_to_create:
            Path(path).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def from_yaml(cls, config_path: str, base_config: BaseConfig) -> 'DashboardConfig':
        """
        Create configuration from YAML file.
        
        Args:
            config_path: Path to YAML configuration file
            base_config: Base system configuration
            
        Returns:
            DashboardConfig instance
        """
        config_path = Path(config_path)
        
        if not config_path.exists():
            # Create default config file
            default_config = cls._get_default_config()
            with open(config_path, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
        
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        return cls(base_config=base_config, **config_data)
    
    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """Get default configuration dictionary."""
        return {
            'host': '127.0.0.1',
            'port': 8081,
            'debug': False,
            'dashboard_title': 'Apex v4 Trading Dashboard',
            'theme': 'dark',
            'update_interval_seconds': 5
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'auto_reload': self.auto_reload,
            'workers': self.workers,
            'dashboard_title': self.dashboard_title,
            'theme': self.theme,
            'update_interval_seconds': self.update_interval_seconds,
            'max_chart_points': self.max_chart_points,
            'timezone': self.timezone,
            'data_sources': self.data_sources,
            'ui_config': self.ui_config,
            'performance_config': self.performance_config,
            'security_config': self.security_config,
            'logging_config': self.logging_config,
            'static_config': self.static_config
        }
    
    def save_to_yaml(self, config_path: str):
        """
        Save configuration to YAML file.
        
        Args:
            config_path: Path to save configuration file
        """
        config_dict = self.to_dict()
        
        with open(config_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def get_data_source_config(self, source_type: str) -> Dict[str, Any]:
        """
        Get configuration for specific data source.
        
        Args:
            source_type: Type of data source
            
        Returns:
            Configuration dictionary for the data source
        """
        return self.data_sources.get(source_type, {})
    
    def is_data_source_enabled(self, source_type: str) -> bool:
        """
        Check if data source is enabled.
        
        Args:
            source_type: Type of data source
            
        Returns:
            True if enabled, False otherwise
        """
        source_config = self.get_data_source_config(source_type)
        return source_config.get('enabled', False)
    
    def get_websocket_config(self) -> Dict[str, Any]:
        """Get WebSocket configuration."""
        return {
            'ping_interval': self.performance_config['websocket_ping_interval'],
            'ping_timeout': self.performance_config['websocket_ping_timeout'],
            'max_size': 1024 * 1024,  # 1MB
            'max_queue': 32
        }
    
    def get_cors_config(self) -> Dict[str, Any]:
        """Get CORS configuration for network-wide access."""
        return {
            'allow_origins': self.security_config['cors_origins'],
            'allow_credentials': True,
            'allow_methods': ['*'],  # Allow all HTTP methods
            'allow_headers': ['*']   # Allow all headers
        }
    
    def validate(self) -> List[str]:
        """
        Validate configuration and return list of issues.
        
        Returns:
            List of configuration issues (empty if valid)
        """
        issues = []
        
        try:
            self._validate_config()
        except ConfigurationError as e:
            issues.append(str(e))
        
        # Additional validation
        if not self.base_config:
            issues.append("Base configuration is required")
        
        return issues
