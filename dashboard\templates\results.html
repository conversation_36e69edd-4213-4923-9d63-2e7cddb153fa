{% extends "base.html" %}

{% block title %}Results Analysis - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">Results Analysis</h2>
                        <p class="text-muted mb-0">Backtest and Forward Test Performance</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-info">Last Updated: <span id="last-update">--:--:--</span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Navigation Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="trading-card">
                <ul class="nav nav-tabs" id="resultsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="backtest-tab" data-bs-toggle="tab" data-bs-target="#backtest" type="button" role="tab">
                            <i class="fas fa-chart-line me-2"></i>Backtest Results
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="forward-tab" data-bs-toggle="tab" data-bs-target="#forward" type="button" role="tab">
                            <i class="fas fa-chart-area me-2"></i>Forward Test
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="comparison-tab" data-bs-toggle="tab" data-bs-target="#comparison" type="button" role="tab">
                            <i class="fas fa-balance-scale me-2"></i>Comparison
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="resultsTabContent">
        <!-- Backtest Results Tab -->
        <div class="tab-pane fade show active" id="backtest" role="tabpanel">
            <div class="row">
                <!-- Performance Summary -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Total Return</h6>
                            <i class="fas fa-chart-line text-success"></i>
                        </div>
                        <div class="metric-value text-success" id="backtest-return">+24.8%</div>
                        <div class="metric-change">
                            <small class="text-muted">Annualized: +31.2%</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Sharpe Ratio</h6>
                            <i class="fas fa-trophy text-warning"></i>
                        </div>
                        <div class="metric-value text-warning" id="backtest-sharpe">2.34</div>
                        <div class="metric-change">
                            <small class="text-success">Excellent</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Max Drawdown</h6>
                            <i class="fas fa-arrow-down text-danger"></i>
                        </div>
                        <div class="metric-value text-danger" id="backtest-drawdown">-5.2%</div>
                        <div class="metric-change">
                            <small class="text-muted">Recovery: 12 days</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Win Rate</h6>
                            <i class="fas fa-percentage text-info"></i>
                        </div>
                        <div class="metric-value text-info" id="backtest-winrate">68.5%</div>
                        <div class="metric-change">
                            <small class="text-muted">1,247 trades</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backtest Charts -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="trading-card">
                        <div class="card-header">
                            <h5 class="mb-0">Equity Curve</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="backtestEquityChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="trading-card">
                        <div class="card-header">
                            <h5 class="mb-0">Monthly Returns</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="backtestMonthlyChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade Statistics -->
            <div class="row">
                <div class="col-12">
                    <div class="trading-card">
                        <div class="card-header">
                            <h5 class="mb-0">Trade Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Value</th>
                                            <th>Benchmark</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="backtest-stats">
                                        <tr>
                                            <td>Total Trades</td>
                                            <td>1,247</td>
                                            <td>1,000+</td>
                                            <td><span class="badge bg-success">Good</span></td>
                                        </tr>
                                        <tr>
                                            <td>Average Trade</td>
                                            <td>$12.45</td>
                                            <td>$10+</td>
                                            <td><span class="badge bg-success">Good</span></td>
                                        </tr>
                                        <tr>
                                            <td>Profit Factor</td>
                                            <td>1.85</td>
                                            <td>1.5+</td>
                                            <td><span class="badge bg-success">Excellent</span></td>
                                        </tr>
                                        <tr>
                                            <td>Calmar Ratio</td>
                                            <td>4.12</td>
                                            <td>3.0+</td>
                                            <td><span class="badge bg-success">Excellent</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forward Test Results Tab -->
        <div class="tab-pane fade" id="forward" role="tabpanel">
            <div class="row">
                <!-- Forward Test Performance -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Live Return</h6>
                            <i class="fas fa-chart-line text-success"></i>
                        </div>
                        <div class="metric-value text-success" id="forward-return">+18.2%</div>
                        <div class="metric-change">
                            <small class="text-muted">30 days</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Live Sharpe</h6>
                            <i class="fas fa-trophy text-warning"></i>
                        </div>
                        <div class="metric-value text-warning" id="forward-sharpe">2.12</div>
                        <div class="metric-change">
                            <small class="text-success">Strong</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Live Drawdown</h6>
                            <i class="fas fa-arrow-down text-danger"></i>
                        </div>
                        <div class="metric-value text-danger" id="forward-drawdown">-3.1%</div>
                        <div class="metric-change">
                            <small class="text-success">Better than backtest</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h6>Live Win Rate</h6>
                            <i class="fas fa-percentage text-info"></i>
                        </div>
                        <div class="metric-value text-info" id="forward-winrate">71.2%</div>
                        <div class="metric-change">
                            <small class="text-success">+2.7% vs backtest</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forward Test Chart -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="trading-card">
                        <div class="card-header">
                            <h5 class="mb-0">Forward Test Performance</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="forwardTestChart" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Tab -->
        <div class="tab-pane fade" id="comparison" role="tabpanel">
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="trading-card">
                        <div class="card-header">
                            <h5 class="mb-0">Backtest vs Forward Test Comparison</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Backtest</th>
                                            <th>Forward Test</th>
                                            <th>Difference</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Annual Return</td>
                                            <td>31.2%</td>
                                            <td>28.8%</td>
                                            <td class="text-warning">-2.4%</td>
                                            <td><span class="badge bg-success">Good</span></td>
                                        </tr>
                                        <tr>
                                            <td>Sharpe Ratio</td>
                                            <td>2.34</td>
                                            <td>2.12</td>
                                            <td class="text-warning">-0.22</td>
                                            <td><span class="badge bg-success">Good</span></td>
                                        </tr>
                                        <tr>
                                            <td>Max Drawdown</td>
                                            <td>-5.2%</td>
                                            <td>-3.1%</td>
                                            <td class="text-success">+2.1%</td>
                                            <td><span class="badge bg-success">Better</span></td>
                                        </tr>
                                        <tr>
                                            <td>Win Rate</td>
                                            <td>68.5%</td>
                                            <td>71.2%</td>
                                            <td class="text-success">+2.7%</td>
                                            <td><span class="badge bg-success">Better</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize results page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeResultsCharts();
    
    // Update last update time
    updateLastUpdateTime();
    
    // Set up real-time updates if WebSocket is available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        // Subscribe to results updates
        console.log('Results page initialized with real-time updates');
    }
});

function initializeResultsCharts() {
    // Initialize backtest equity chart
    const backtestCtx = document.getElementById('backtestEquityChart');
    if (backtestCtx) {
        new Chart(backtestCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Equity Curve',
                    data: [],
                    borderColor: '#00ff88',
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#ffffff' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    }
}

function updateLastUpdateTime() {
    const timeElement = document.getElementById('last-update');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString();
    }
}
</script>
{% endblock %}
