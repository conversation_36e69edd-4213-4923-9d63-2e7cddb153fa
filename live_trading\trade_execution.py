"""
Trade Execution Engine

Handles MT5 trade execution with model-driven entry/exit optimization,
multi-tier TP/SL system, and comprehensive position management.
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import time
import uuid

from .base import LiveTrade, TradeDirection, TradeStatus, OrderType

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.mt5_collector.mt5_client import MT5Client
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import MT5DataError


class TradeExecutionEngine(LoggerMixin):
    """
    Advanced trade execution engine with model-driven optimization.
    
    Implements sophisticated entry/exit logic, multi-tier TP/SL system,
    and real-time position management with MT5 integration.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize trade execution engine.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.symbol = config.get('symbol', 'XAUUSD!')
        
        # MT5 client
        self.client = MT5Client()
        self.is_connected = False
        
        # Execution parameters
        self.spread_pips = config.get('spread_pips', 0.18)
        self.slippage_pips = config.get('slippage_pips', 0.05)
        self.max_slippage_pips = config.get('max_slippage_pips', 0.5)
        
        # Multi-tier TP/SL configuration
        self.tp1_percentage = config.get('tp1_percentage', 0.4)  # 40% quick profits
        self.tp2_percentage = config.get('tp2_percentage', 0.35) # 35% swing profits
        self.tp3_percentage = config.get('tp3_percentage', 0.25) # 25% trend following
        
        # Model-driven parameters
        self.use_model_entry_timing = config.get('use_model_entry_timing', True)
        self.use_model_exit_levels = config.get('use_model_exit_levels', True)
        self.entry_patience_seconds = config.get('entry_patience_seconds', 300)
        
        # Execution tracking
        self.execution_stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'avg_execution_time_ms': 0.0,
            'avg_slippage_pips': 0.0,
            'total_spread_cost': 0.0,
            'total_commission': 0.0,
            'last_execution_time': None,
            'errors': 0
        }
        
        # Active orders tracking
        self.pending_orders = {}
        self.active_positions = {}
    
    def connect(self) -> bool:
        """Connect to MT5 for trade execution."""
        try:
            if self.client.connect():
                self.is_connected = True
                
                # Verify symbol and trading permissions
                symbol_info = self.client.get_symbol_info(self.symbol)
                if not symbol_info:
                    raise MT5DataError(f"Symbol {self.symbol} not available for trading")
                
                # Check if trading is allowed
                account_info = self.client.account_info
                if not account_info:
                    raise MT5DataError("Failed to get account information")

                # Note: MT5 account info doesn't have trade_allowed field directly
                # We'll assume trading is allowed if we can get account info
                self.logger.info(f"Account balance: ${account_info.get('balance', 0):,.2f}")
                
                self.logger.info(f"Connected to MT5 for trade execution: {self.symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect for trade execution: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5."""
        if self.is_connected:
            self.client.disconnect()
            self.is_connected = False
            self.logger.info("Disconnected from MT5 trade execution")
    
    def execute_model_driven_trade(self, signal: Dict[str, Any], 
                                 current_price: float,
                                 account_balance: float) -> Optional[LiveTrade]:
        """
        Execute trade with model-driven optimization.
        
        Args:
            signal: Trading signal from model engine
            current_price: Current market price
            account_balance: Current account balance
            
        Returns:
            LiveTrade object if successful, None otherwise
        """
        if not self.is_connected:
            if not self.connect():
                return None
        
        if not signal.get('should_enter', False):
            return None
        
        try:
            start_time = time.time()
            
            # Calculate position size with model-driven adjustments
            position_size = self._calculate_position_size(
                signal, account_balance, current_price
            )
            
            if position_size <= 0:
                self.logger.warning("Position size calculation resulted in zero or negative size")
                return None
            
            # Determine optimal entry price and method
            entry_result = self._determine_optimal_entry(signal, current_price)
            
            # Create trade object
            trade = LiveTrade(
                trade_id=str(uuid.uuid4()),
                symbol=self.symbol,
                direction=TradeDirection.LONG if signal['direction'] == 'long' else TradeDirection.SHORT,
                entry_time=datetime.now(),
                entry_price=entry_result['target_price'],
                entry_type=entry_result['order_type'],
                position_size=position_size,
                model_confidence=signal.get('confidence', 0.0),
                model_consensus={},  # Could be populated with individual model scores
                signal_probability=signal.get('confidence', 0.0)
            )
            
            # Calculate model-driven TP/SL levels
            tp_sl_levels = self._calculate_model_driven_levels(signal, entry_result['target_price'])
            trade.tp1_price = tp_sl_levels['tp1_price']
            trade.tp2_price = tp_sl_levels['tp2_price']
            trade.tp3_price = tp_sl_levels['tp3_price']
            trade.sl_price = tp_sl_levels['sl_price']
            
            # Execute the trade
            execution_result = self._execute_mt5_order(trade, entry_result)
            
            if execution_result['success']:
                trade.status = TradeStatus.OPEN
                trade.entry_price = execution_result['fill_price']
                trade.spread_cost = execution_result['spread_cost']
                trade.slippage_cost = execution_result['slippage_cost']
                
                # Set up TP/SL orders
                self._setup_multi_tier_exits(trade)
                
                # Update statistics
                execution_time_ms = (time.time() - start_time) * 1000
                self._update_execution_stats(execution_result, execution_time_ms)
                
                self.logger.info(f"✓ Trade executed: {trade.trade_id} - "
                               f"{trade.direction.value} {trade.position_size} lots at {trade.entry_price}")
                
                return trade
            else:
                self.logger.error(f"Trade execution failed: {execution_result['error']}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error executing model-driven trade: {str(e)}")
            self.execution_stats['errors'] += 1
            return None
    
    def _calculate_position_size(self, signal: Dict[str, Any], 
                               account_balance: float, current_price: float) -> float:
        """Calculate position size with model-driven adjustments."""
        # Base risk calculation
        max_risk_amount = account_balance * self.config.get('max_risk_per_trade', 0.02)
        sl_distance = signal.get('sl_distance', 20.0)
        
        # Calculate base position size
        pip_value = 1.0  # For XAUUSD, approximately $1 per pip per 0.01 lot
        base_position_size = max_risk_amount / (sl_distance * pip_value)
        
        # Apply model-driven multipliers
        confidence_multiplier = signal.get('position_size_multiplier', 1.0)
        volatility_adjustment = signal.get('volatility_adjustment', 1.0)
        
        # Session-based adjustment
        current_hour = datetime.now().hour
        session_config = self.config.get('trading_sessions', {})
        session_multiplier = 1.0
        
        for session_name, session_data in session_config.items():
            start_hour = session_data.get('start_hour', 0)
            end_hour = session_data.get('end_hour', 24)
            if start_hour <= current_hour < end_hour:
                session_multiplier = session_data.get('position_multiplier', 1.0)
                break
        
        # Final position size calculation
        final_position_size = (
            base_position_size * 
            confidence_multiplier * 
            volatility_adjustment * 
            session_multiplier
        )
        
        # Apply limits
        min_lot = 0.01
        max_lot = self.config.get('max_position_size', 10.0)
        final_position_size = max(min_lot, min(final_position_size, max_lot))
        
        # Round to valid lot size
        return round(final_position_size, 2)
    
    def _determine_optimal_entry(self, signal: Dict[str, Any], 
                               current_price: float) -> Dict[str, Any]:
        """Determine optimal entry price and order type."""
        if not self.use_model_entry_timing:
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Model-driven entry optimization
        entry_urgency = signal.get('entry_urgency', 'normal')
        confidence = signal.get('confidence', 0.0)
        volatility_adjustment = signal.get('volatility_adjustment', 1.0)
        
        if entry_urgency == 'high' or confidence > 0.85:
            # High confidence - immediate market entry
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Calculate optimal entry offset
        direction = signal.get('direction', 'long')
        base_offset = 2.0 * volatility_adjustment  # Base offset in pips
        
        if direction == 'long':
            target_price = current_price - (base_offset * 0.01)  # Better entry for long
        else:
            target_price = current_price + (base_offset * 0.01)  # Better entry for short
        
        return {
            'target_price': target_price,
            'order_type': OrderType.LIMIT,
            'patience_seconds': self.entry_patience_seconds
        }
    
    def _calculate_model_driven_levels(self, signal: Dict[str, Any], 
                                     entry_price: float) -> Dict[str, float]:
        """Calculate TP/SL levels using model predictions."""
        direction = signal.get('direction', 'long')
        direction_multiplier = 1 if direction == 'long' else -1
        
        # Get model-predicted distances
        tp1_distance = signal.get('tp1_distance', 15.0)
        tp2_distance = signal.get('tp2_distance', 30.0)
        sl_distance = signal.get('sl_distance', 20.0)
        
        # Apply volatility adjustment
        vol_adjustment = signal.get('volatility_adjustment', 1.0)
        tp1_distance *= vol_adjustment
        tp2_distance *= vol_adjustment
        sl_distance *= vol_adjustment
        
        # Calculate actual price levels
        tp1_price = entry_price + (direction_multiplier * tp1_distance * 0.01)
        tp2_price = entry_price + (direction_multiplier * tp2_distance * 0.01)
        tp3_price = entry_price + (direction_multiplier * tp2_distance * 1.5 * 0.01)  # Extended target
        sl_price = entry_price - (direction_multiplier * sl_distance * 0.01)
        
        return {
            'tp1_price': tp1_price,
            'tp2_price': tp2_price,
            'tp3_price': tp3_price,
            'sl_price': sl_price
        }
    
    def _execute_mt5_order(self, trade: LiveTrade, entry_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute order with MT5."""
        try:
            # Prepare order request
            order_type = mt5.ORDER_TYPE_BUY if trade.direction == TradeDirection.LONG else mt5.ORDER_TYPE_SELL
            
            if entry_result['order_type'] == OrderType.LIMIT:
                order_type = mt5.ORDER_TYPE_BUY_LIMIT if trade.direction == TradeDirection.LONG else mt5.ORDER_TYPE_SELL_LIMIT
            
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": trade.position_size,
                "type": order_type,
                "price": entry_result['target_price'],
                "sl": trade.sl_price,
                "tp": trade.tp1_price,  # Set first TP initially
                "deviation": int(self.max_slippage_pips * 10),  # Points
                "magic": 12345,  # Magic number for identification
                "comment": f"AI_Trade_{trade.trade_id[:8]}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result is None:
                return {
                    'success': False,
                    'error': f"Order send failed: {mt5.last_error()}"
                }
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    'success': False,
                    'error': f"Order rejected: {result.retcode} - {result.comment}"
                }
            
            # Calculate costs
            fill_price = result.price
            spread_cost = self.spread_pips * trade.position_size * 1.0  # $1 per pip per 0.01 lot
            slippage_cost = abs(fill_price - entry_result['target_price']) * trade.position_size * 100
            
            return {
                'success': True,
                'order_ticket': result.order,
                'fill_price': fill_price,
                'spread_cost': spread_cost,
                'slippage_cost': slippage_cost,
                'commission': 0.0  # Assuming no commission
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"MT5 execution error: {str(e)}"
            }
    
    def _setup_multi_tier_exits(self, trade: LiveTrade):
        """Set up multi-tier TP/SL system."""
        try:
            # This would involve setting up multiple TP orders
            # For now, we'll track this in our system and manage manually
            self.logger.info(f"Multi-tier exits configured for trade {trade.trade_id}")
            
            # In a full implementation, you would:
            # 1. Place multiple TP orders at different levels
            # 2. Set up trailing stop logic
            # 3. Implement dynamic exit level adjustments
            
        except Exception as e:
            self.logger.error(f"Failed to setup multi-tier exits: {str(e)}")
    
    def _update_execution_stats(self, execution_result: Dict[str, Any], execution_time_ms: float):
        """Update execution statistics."""
        self.execution_stats['orders_placed'] += 1
        
        if execution_result['success']:
            self.execution_stats['orders_filled'] += 1
            
            # Update averages
            total_filled = self.execution_stats['orders_filled']
            
            self.execution_stats['avg_execution_time_ms'] = (
                (self.execution_stats['avg_execution_time_ms'] * (total_filled - 1) + execution_time_ms) / total_filled
            )
            
            slippage_pips = execution_result.get('slippage_cost', 0.0) / 100  # Convert to pips
            self.execution_stats['avg_slippage_pips'] = (
                (self.execution_stats['avg_slippage_pips'] * (total_filled - 1) + slippage_pips) / total_filled
            )
            
            self.execution_stats['total_spread_cost'] += execution_result.get('spread_cost', 0.0)
            
        else:
            self.execution_stats['orders_rejected'] += 1
        
        self.execution_stats['last_execution_time'] = datetime.now()
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution performance statistics."""
        total_orders = self.execution_stats['orders_placed']
        fill_rate = self.execution_stats['orders_filled'] / max(total_orders, 1)
        
        return {
            **self.execution_stats,
            'fill_rate': fill_rate,
            'rejection_rate': self.execution_stats['orders_rejected'] / max(total_orders, 1),
            'is_connected': self.is_connected,
            'pending_orders_count': len(self.pending_orders),
            'active_positions_count': len(self.active_positions)
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
